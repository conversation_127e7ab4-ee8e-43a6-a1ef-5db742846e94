#!/usr/bin/env python3
"""
Script Simple para Evaluar Completitud OMOP
Evalúa qué tan completos están tus datos para mapear a OMOP
"""

import pandas as pd
import numpy as np
from datetime import datetime

def evaluar_completitud_omop(csv_path):
    """
    Evalúa la completitud de los datos para mapeo OMOP
    """
    print("🔍 EVALUANDO COMPLETITUD OMOP")
    print("=" * 50)
    
    # Cargar datos
    df = pd.read_csv(csv_path)
    total_records = len(df)
    
    print(f"📊 Total de registros: {total_records:,}")
    print(f"👥 Pacientes únicos: {df['aio_patient_id'].nunique():,}")
    print(f"🏥 Encuentros únicos: {df['case'].nunique():,}")
    print()
    
    # Evaluar por tabla OMOP
    resultados = {}
    
    # 1. PERSON Table - Nivel de completitud
    print("🧑 TABLA PERSON (Pacientes)")
    print("-" * 30)
    
    person_campos = {
        'aio_patient_id': 'person_id (OBLIGATORIO)',
        # No tenemos género, edad, etc. en este CSV
    }
    
    person_score = 0
    person_max = 0
    
    for campo, descripcion in person_campos.items():
        if campo in df.columns:
            completitud = (df[campo].notna().sum() / total_records) * 100
            print(f"  ✅ {descripcion}: {completitud:.1f}%")
            if completitud >= 90:
                person_score += 1
            person_max += 1
        else:
            print(f"  ❌ {descripcion}: Campo no disponible")
            person_max += 1
    
    # Faltan campos demográficos críticos
    print(f"  ❌ gender_concept_id (OBLIGATORIO): No disponible")
    print(f"  ❌ year_of_birth (OBLIGATORIO): No disponible")
    print(f"  ❌ race_concept_id (OBLIGATORIO): No disponible")
    print(f"  ❌ ethnicity_concept_id (OBLIGATORIO): No disponible")
    person_max += 4
    
    resultados['PERSON'] = (person_score / person_max) * 100
    print(f"  📈 SCORE PERSON: {resultados['PERSON']:.1f}%")
    print()
    
    # 2. VISIT_OCCURRENCE Table
    print("🏥 TABLA VISIT_OCCURRENCE (Encuentros)")
    print("-" * 40)
    
    visit_campos = {
        'case': 'visit_occurrence_id (OBLIGATORIO)',
        'aio_patient_id': 'person_id (OBLIGATORIO)', 
        'encounter_start_date': 'visit_start_date (OBLIGATORIO)',
        'encounter_end_date': 'visit_end_date (OBLIGATORIO)',
        'case_type': 'visit_concept_id (OBLIGATORIO)',
        'provider_id': 'provider_id (IMPORTANTE)',
        'institution_name': 'care_site_id (IMPORTANTE)'
    }
    
    visit_score = 0
    visit_max = 0
    
    for campo, descripcion in visit_campos.items():
        if campo in df.columns:
            completitud = (df[campo].notna().sum() / total_records) * 100
            if completitud >= 95:
                status = "✅"
                if "OBLIGATORIO" in descripcion:
                    visit_score += 2
                else:
                    visit_score += 1
            elif completitud >= 80:
                status = "⚠️"
                if "OBLIGATORIO" in descripcion:
                    visit_score += 1
                else:
                    visit_score += 0.5
            else:
                status = "❌"
            
            print(f"  {status} {descripcion}: {completitud:.1f}%")
            
            if "OBLIGATORIO" in descripcion:
                visit_max += 2
            else:
                visit_max += 1
        else:
            print(f"  ❌ {descripcion}: Campo no disponible")
            if "OBLIGATORIO" in descripcion:
                visit_max += 2
            else:
                visit_max += 1
    
    resultados['VISIT_OCCURRENCE'] = (visit_score / visit_max) * 100
    print(f"  📈 SCORE VISIT: {resultados['VISIT_OCCURRENCE']:.1f}%")
    print()
    
    # 3. PROCEDURE_OCCURRENCE Table
    print("⚕️ TABLA PROCEDURE_OCCURRENCE (Procedimientos)")
    print("-" * 45)
    
    # Analizar tipos de actividades
    if 'act_type_desc' in df.columns:
        actividades = df['act_type_desc'].value_counts()
        print("  📋 Tipos de actividades encontradas:")
        for tipo, count in actividades.items():
            pct = (count / total_records) * 100
            print(f"    • {tipo}: {count:,} registros ({pct:.1f}%)")
        print()
    
    procedure_campos = {
        'activity_id': 'procedure_occurrence_id (OBLIGATORIO)',
        'aio_patient_id': 'person_id (OBLIGATORIO)',
        'code_activity': 'procedure_concept_id (OBLIGATORIO)', 
        'start_activity_date': 'procedure_date (OBLIGATORIO)',
        'type_activity': 'procedure_type_concept_id (OBLIGATORIO)',
        'case': 'visit_occurrence_id (IMPORTANTE)',
        'code_activity': 'procedure_source_value (IMPORTANTE)'
    }
    
    procedure_score = 0
    procedure_max = 0
    
    for campo, descripcion in procedure_campos.items():
        if campo in df.columns:
            completitud = (df[campo].notna().sum() / total_records) * 100
            if completitud >= 95:
                status = "✅"
                if "OBLIGATORIO" in descripcion:
                    procedure_score += 2
                else:
                    procedure_score += 1
            elif completitud >= 80:
                status = "⚠️"
                if "OBLIGATORIO" in descripcion:
                    procedure_score += 1
                else:
                    procedure_score += 0.5
            else:
                status = "❌"
            
            print(f"  {status} {descripcion}: {completitud:.1f}%")
            
            if "OBLIGATORIO" in descripcion:
                procedure_max += 2
            else:
                procedure_max += 1
    
    resultados['PROCEDURE_OCCURRENCE'] = (procedure_score / procedure_max) * 100
    print(f"  📈 SCORE PROCEDURES: {resultados['PROCEDURE_OCCURRENCE']:.1f}%")
    print()
    
    # 4. COST Table
    print("💰 TABLA COST (Costos)")
    print("-" * 25)
    
    cost_campos = {
        'gross': 'total_charge (IMPORTANTE)',
        'net': 'total_cost (IMPORTANTE)',
        'patient_share': 'paid_by_patient (IMPORTANTE)',
        'payment_amount': 'total_paid (IMPORTANTE)'
    }
    
    cost_score = 0
    cost_max = 0
    
    for campo, descripcion in cost_campos.items():
        if campo in df.columns:
            completitud = (df[campo].notna().sum() / total_records) * 100
            if completitud >= 95:
                status = "✅"
                cost_score += 1
            elif completitud >= 80:
                status = "⚠️"
                cost_score += 0.5
            else:
                status = "❌"
            
            print(f"  {status} {descripcion}: {completitud:.1f}%")
            cost_max += 1
    
    resultados['COST'] = (cost_score / cost_max) * 100
    print(f"  📈 SCORE COST: {resultados['COST']:.1f}%")
    print()
    
    # RESUMEN FINAL
    print("🎯 RESUMEN DE COMPLETITUD OMOP")
    print("=" * 50)
    
    for tabla, score in resultados.items():
        if score >= 80:
            status = "✅ EXCELENTE"
        elif score >= 60:
            status = "⚠️ ACEPTABLE" 
        elif score >= 40:
            status = "🔶 LIMITADO"
        else:
            status = "❌ INSUFICIENTE"
        
        print(f"{tabla:<20} {score:>6.1f}% {status}")
    
    score_general = np.mean(list(resultados.values()))
    print(f"\n{'SCORE GENERAL':<20} {score_general:>6.1f}%")
    
    # RECOMENDACIONES
    print("\n💡 RECOMENDACIONES")
    print("-" * 20)
    
    if score_general >= 70:
        print("✅ Tus datos son VIABLES para OMOP")
        print("📋 Puedes proceder con la implementación")
    elif score_general >= 50:
        print("⚠️ Tus datos son PARCIALMENTE viables")
        print("📋 Necesitas campos adicionales para calidad completa")
    else:
        print("❌ Tus datos necesitan MEJORAS significativas")
        print("📋 Considera obtener más campos demográficos")
    
    print("\n🔧 Próximos pasos:")
    print("1. Agregar datos demográficos (edad, género)")
    print("2. Mapear códigos CPT a conceptos OMOP") 
    print("3. Configurar Data Quality Dashboard")
    print("4. Implementar ETL con validación de completitud")
    
    return resultados

if __name__ == "__main__":
    # Evaluar el CSV de Abu Dhabi
    csv_path = "/Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Work/AIO/fhir-omop/data/real_test_datasets/claim_anonymized.csv"
    
    try:
        resultados = evaluar_completitud_omop(csv_path)
    except FileNotFoundError:
        print("❌ Error: No se encontró el archivo CSV")
        print(f"   Verifica la ruta: {csv_path}")
    except Exception as e:
        print(f"❌ Error inesperado: {e}")
