#!/usr/bin/env python3
"""
Validación Continua de Completitud OMOP - Script Simple
Usa esto para monitorear la calidad de tus datos OMOP
"""

import pandas as pd
from datetime import datetime

def validar_tabla_person(df_person):
    """Valida completitud de tabla PERSON"""
    print("🧑 Validando PERSON...")
    
    # Campos OBLIGATORIOS (100%)
    obligatorios = ['person_id', 'gender_concept_id', 'year_of_birth', 
                   'race_concept_id', 'ethnicity_concept_id']
    
    total = len(df_person)
    errores = []
    
    for campo in obligatorios:
        if campo in df_person.columns:
            nulos = df_person[campo].isna().sum()
            completitud = ((total - nulos) / total) * 100
            
            if completitud < 100:
                errores.append(f"❌ {campo}: {completitud:.1f}% (Requiere 100%)")
            else:
                print(f"✅ {campo}: {completitud:.1f}%")
        else:
            errores.append(f"❌ {campo}: Campo faltante")
    
    return errores

def validar_tabla_visit(df_visit):
    """Valida completitud de tabla VISIT_OCCURRENCE"""
    print("🏥 Validando VISIT_OCCURRENCE...")
    
    obligatorios = ['visit_occurrence_id', 'person_id', 'visit_concept_id',
                   'visit_start_date', 'visit_end_date', 'visit_type_concept_id']
    
    total = len(df_visit)
    errores = []
    
    for campo in obligatorios:
        if campo in df_visit.columns:
            nulos = df_visit[campo].isna().sum()
            completitud = ((total - nulos) / total) * 100
            
            if completitud < 100:
                errores.append(f"❌ {campo}: {completitud:.1f}% (Requiere 100%)")
            else:
                print(f"✅ {campo}: {completitud:.1f}%")
        else:
            errores.append(f"❌ {campo}: Campo faltante")
    
    # Validar fechas lógicas
    if 'visit_start_date' in df_visit.columns and 'visit_end_date' in df_visit.columns:
        fechas_invalidas = df_visit[df_visit['visit_end_date'] < df_visit['visit_start_date']]
        if len(fechas_invalidas) > 0:
            errores.append(f"❌ {len(fechas_invalidas)} visitas con fecha fin < fecha inicio")
    
    return errores

def validar_completitud_general(data_path):
    """Función principal de validación"""
    print("🔍 VALIDACIÓN DE COMPLETITUD OMOP")
    print("=" * 40)
    print(f"📅 Fecha: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    try:
        # Simular carga de datos OMOP (adaptalo a tu estructura)
        df = pd.read_csv(data_path)
        
        total_errores = []
        
        # Validar cada tabla
        errores_person = validar_tabla_person(df)  # Adaptar según tu estructura
        errores_visit = validar_tabla_visit(df)    # Adaptar según tu estructura
        
        total_errores.extend(errores_person)
        total_errores.extend(errores_visit)
        
        # Reporte final
        print("\n📋 REPORTE FINAL")
        print("-" * 20)
        
        if len(total_errores) == 0:
            print("✅ TODOS LOS CRITERIOS CUMPLIDOS")
            print("🎯 Tu implementación OMOP está completa")
        else:
            print(f"⚠️ {len(total_errores)} PROBLEMAS ENCONTRADOS:")
            for error in total_errores:
                print(f"   {error}")
            
            print("\n💡 ACCIONES RECOMENDADAS:")
            print("1. Corregir campos obligatorios faltantes")
            print("2. Validar lógica de fechas")
            print("3. Re-ejecutar validación")
        
        return len(total_errores) == 0
        
    except Exception as e:
        print(f"❌ Error durante validación: {e}")
        return False

# Ejemplo de uso para tu proyecto
if __name__ == "__main__":
    # Ruta a tus datos
    csv_path = "/Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Work/AIO/fhir-omop/data/real_test_datasets/claim_anonymized.csv"
    
    # Ejecutar validación
    es_valido = validar_completitud_general(csv_path)
    
    if es_valido:
        print("\n🚀 LISTO PARA PRODUCCIÓN")
    else:
        print("\n🔧 NECESITA CORRECCIONES")
