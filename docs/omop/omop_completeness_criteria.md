# OMOP CDM Completeness Criteria

## Overview

This document defines objective completeness criteria for OMOP Common Data Model (CDM) v5.4 tables based on official OHDSI documentation, Data Quality Dashboard standards, and THEMIS conventions. The criteria are derived from extensive analysis of OHDSI community standards and real-world implementation experiences.

## Sources and Standards

### Primary Sources
1. **OHDSI Data Quality Dashboard (DQD)**: Provides 24 check types with configurable thresholds across 3,500+ quality checks
2. **THEMIS Conventions**: Community-ratified field population rules and standards
3. **OMOP CDM v5.4 Specifications**: Official table and field definitions
4. **OHDSI Implementation Guidelines**: Best practices from community implementations

### Data Quality Dashboard Framework
The DQD organizes checks according to the Kahn Framework categories:
- **Completeness**: Field population and NULL value checks
- **Conformance**: Data type and format validation  
- **Plausibility**: Value range and temporal consistency
- **Completeness Thresholds**: CSV-configurable failure tolerances (0-100%)

## Completeness Assessment Framework

### Completeness Types
1. **Required Field Completeness**: Mandatory fields must be 100% populated (isRequired=Yes, threshold=0%)
2. **Expected Field Completeness**: Important optional fields should meet minimum thresholds  
3. **Table Population Completeness**: Core tables should contain data for active patients
4. **Cross-Table Consistency**: Foreign key relationships and referential integrity

### Quality Check Categories
- **isRequired Checks**: Fields marked as required with 0% failure threshold
- **measureValueCompleteness**: Percentage of non-NULL values in important fields
- **standardConceptRecordCompleteness**: Standard concept population rates
- **sourceValueCompleteness**: Source value preservation for traceability

## Core Clinical Tables Completeness Criteria

### PERSON Table
**Objective**: 100% completeness for required demographic fields

**Required Fields (isRequired=Yes, threshold=0%):**
- `person_id` - Unique identifier (Primary Key)
- `gender_concept_id` - Gender concept from OMOP vocabulary (8507=MALE, 8532=FEMALE, 8551=UNKNOWN)
- `year_of_birth` - Birth year (valid range: 1900-current year)
- `race_concept_id` - Race concept from OMOP vocabulary (0=No matching concept, 8552=UNKNOWN)
- `ethnicity_concept_id` - Ethnicity concept from OMOP vocabulary (0=No matching concept, 8552=UNKNOWN)

**Expected Fields (Recommended DQD thresholds):**
- `month_of_birth` - ≥80% completeness when source data available
- `day_of_birth` - ≥80% completeness when source data available  
- `birth_datetime` - ≥80% completeness when precise timestamps available
- `location_id` - ≥70% completeness for geographic analysis capabilities
- `provider_id` - ≥60% completeness for primary care provider linkage
- `care_site_id` - ≥60% completeness for primary care site linkage

**THEMIS Convention Requirements:**
- Gender must use standard concepts rather than NULL values
- Unknown demographics should use OMOP "Unknown" concepts (8552, 8551) rather than NULL
- Year of birth must be plausible (within human lifespan constraints)

### OBSERVATION_PERIOD Table
**Objective**: 100% completeness for required fields, comprehensive patient coverage

**Required Fields (isRequired=Yes, threshold=0%):**
- `observation_period_id` - Unique identifier (Primary Key)
- `person_id` - Reference to PERSON table (Foreign Key)
- `observation_period_start_date` - Start of observation (Valid date)
- `observation_period_end_date` - End of observation (Valid date ≥ start_date)
- `period_type_concept_id` - Type concept from Period Type domain (44814724=Period inferred from EHR)

**Coverage Requirements:**
- 100% of persons in PERSON table must have at least one observation period
- Observation periods cannot overlap for the same person
- Gaps between periods are acceptable and indicate inactive observation

**Data Quality Rules:**
- End date must be ≥ start date (temporal consistency check)
- Observation periods should align with actual data presence in clinical tables
- Period type concept must be valid and appropriate for data source

### VISIT_OCCURRENCE Table
**Objective**: Complete representation of healthcare encounters

**Required Fields (isRequired=Yes, threshold=0%):**
- `visit_occurrence_id` - Unique identifier (Primary Key)
- `person_id` - Reference to PERSON table (Foreign Key)
- `visit_concept_id` - Visit type concept (9201=Inpatient, 9202=Outpatient, 9203=Emergency)
- `visit_start_date` - Visit start date (Valid date)
- `visit_end_date` - Visit end date (Valid date ≥ start_date)
- `visit_type_concept_id` - Visit type concept (32827=EHR encounter record)

**Expected Fields (DQD recommended thresholds):**
- `care_site_id` - ≥80% completeness for facility tracking and geographic analysis
- `visit_source_value` - ≥90% completeness for source system traceability
- `provider_id` - ≥70% completeness for attending provider identification
- `admitting_source_concept_id` - ≥70% completeness for inpatient visits only
- `discharge_to_concept_id` - ≥70% completeness for inpatient visits only
- `visit_source_concept_id` - ≥60% completeness for source vocabulary mapping

**Visit Type Standards:**
- Use standard visit concepts: 9201=Inpatient Visit, 9202=Outpatient Visit, 9203=Emergency Room Visit
- Combination visits: 262=Emergency Room and Inpatient Visit, 8971=Intensive Care
- Visit dates must fall within patient's observation periods

### CONDITION_OCCURRENCE Table
**Objective**: Complete diagnosis recording

**Required Fields (Must be 100% complete):**
- `condition_occurrence_id` - Unique identifier
- `person_id` - Reference to PERSON table
- `condition_concept_id` - Condition concept from OMOP vocabulary
- `condition_start_date` - Condition start date
- `condition_type_concept_id` - Condition type concept

**Expected Fields (Recommended thresholds):**
- `condition_source_value` - ≥95% completeness for source traceability
- `visit_occurrence_id` - ≥85% completeness for encounter linkage
- `condition_status_concept_id` - ≥70% when status information available

### DRUG_EXPOSURE Table
**Objective**: Complete medication recording

**Required Fields (Must be 100% complete):**
- `drug_exposure_id` - Unique identifier
- `person_id` - Reference to PERSON table
- `drug_concept_id` - Drug concept from OMOP vocabulary
- `drug_exposure_start_date` - Exposure start date
- `drug_exposure_end_date` - Exposure end date
- `drug_type_concept_id` - Drug type concept

**Expected Fields (Recommended thresholds):**
- `drug_source_value` - ≥95% completeness for source traceability
- `visit_occurrence_id` - ≥80% completeness for encounter linkage
- `quantity` - ≥70% completeness for dosing analysis
- `days_supply` - ≥70% completeness for duration analysis

### PROCEDURE_OCCURRENCE Table
**Objective**: Complete procedure recording

**Required Fields (Must be 100% complete):**
- `procedure_occurrence_id` - Unique identifier
- `person_id` - Reference to PERSON table
- `procedure_concept_id` - Procedure concept from OMOP vocabulary
- `procedure_date` - Procedure date
- `procedure_type_concept_id` - Procedure type concept

**Expected Fields (Recommended thresholds):**
- `procedure_source_value` - ≥95% completeness for source traceability
- `visit_occurrence_id` - ≥85% completeness for encounter linkage
- `modifier_concept_id` - ≥60% when modifiers are applicable

### MEASUREMENT Table
**Objective**: Complete laboratory and vital signs recording

**Required Fields (Must be 100% complete):**
- `measurement_id` - Unique identifier
- `person_id` - Reference to PERSON table
- `measurement_concept_id` - Measurement concept from OMOP vocabulary
- `measurement_date` - Measurement date
- `measurement_type_concept_id` - Measurement type concept

**Expected Fields (Recommended thresholds):**
- `value_as_number` - ≥85% completeness for quantitative measurements
- `value_as_concept_id` - ≥85% completeness for qualitative measurements
- `unit_concept_id` - ≥95% completeness when value_as_number is populated
- `visit_occurrence_id` - ≥80% completeness for encounter linkage

### OBSERVATION Table
**Objective**: Complete clinical observations recording

**Required Fields (Must be 100% complete):**
- `observation_id` - Unique identifier
- `person_id` - Reference to PERSON table
- `observation_concept_id` - Observation concept from OMOP vocabulary
- `observation_date` - Observation date
- `observation_type_concept_id` - Observation type concept

**Expected Fields (Recommended thresholds):**
- `value_as_string` - ≥80% when applicable
- `value_as_concept_id` - ≥80% for coded observations
- `visit_occurrence_id` - ≥75% completeness for encounter linkage

### DEVICE_EXPOSURE Table
**Objective**: Complete device usage recording

**Required Fields (Must be 100% complete):**
- `device_exposure_id` - Unique identifier
- `person_id` - Reference to PERSON table
- `device_concept_id` - Device concept from OMOP vocabulary
- `device_exposure_start_date` - Exposure start date
- `device_type_concept_id` - Device type concept

**Expected Fields (Recommended thresholds):**
- `device_source_value` - ≥90% completeness for source traceability
- `visit_occurrence_id` - ≥75% completeness for encounter linkage

## Vocabulary and Reference Tables

### CONCEPT Table
**Objective**: Complete vocabulary coverage
- Must include all standard concepts referenced in clinical tables
- Should maintain vocabulary versioning consistency

### CONCEPT_RELATIONSHIP Table
**Objective**: Complete concept mappings
- Required for source-to-standard concept mappings
- Critical for terminology harmonization

## Assessment Implementation

### Automated Checks
1. **Required Field Validation**: SQL queries to identify NULL values in required fields
2. **Threshold Validation**: Percentage calculations for expected field completeness
3. **Referential Integrity**: Foreign key validation across tables
4. **Temporal Consistency**: Date range validations

### Quality Metrics
- **Table Completeness Score**: Weighted average of field completeness percentages
- **Overall CDM Completeness**: Aggregate score across all tables
- **Critical Field Coverage**: Percentage of required fields meeting 100% threshold

### Reporting Framework
- Daily completeness monitoring
- Trend analysis over time
- Exception reporting for critical gaps
- Source system feedback for improvement

## References
- OMOP CDM v5.4 Specifications: https://ohdsi.github.io/CommonDataModel/cdm54.html
- OHDSI Data Quality Dashboard: https://github.com/OHDSI/DataQualityDashboard
- THEMIS Conventions: https://ohdsi.github.io/Themis/
