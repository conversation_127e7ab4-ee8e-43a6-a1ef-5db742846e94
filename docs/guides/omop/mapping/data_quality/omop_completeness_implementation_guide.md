# OMOP Completeness Criteria - Implementation Summary

## Executive Summary

This document provides a consolidated summary of OMOP CDM completeness requirements based on comprehensive analysis of OHDSI standards, Data Quality Dashboard configurations, and THEMIS conventions. The analysis establishes objective criteria for field population requirements, completeness thresholds, and quality standards for OMOP CDM implementations.

## Key Findings

### Data Quality Dashboard (DQD) Standards
- **24 check types** organized by Kahn Framework categories (Completeness, Conformance, Plausibility)
- **3,500+ quality checks** with CSV-configurable thresholds (0-100% failure tolerance)
- **Threshold categories**: 
  - Required fields (isRequired=Yes): 0% failure threshold
  - Important optional fields: 5-30% failure threshold  
  - Contextual fields: 40-60% failure threshold

### THEMIS Convention Requirements
- **Standard concept usage**: Prefer OMOP standard concepts over NULL values
- **Unknown value handling**: Use concept_id 8552 (Unknown) rather than NULL for missing demographics
- **Value co-occurrence**: VALUE_AS_NUMBER and VALUE_AS_CONCEPT_ID are not mutually exclusive in MEASUREMENT table
- **Temporal consistency**: All dates must be logically consistent and within observation periods

## Critical Completeness Thresholds

### Tier 1: Mandatory Fields (100% Required)
All tables must have 100% completeness for:
- **Primary Keys**: All _id fields serving as primary keys
- **Person References**: person_id foreign keys in all clinical tables
- **Core Dates**: Primary date fields (visit_start_date, condition_start_date, etc.)
- **Domain Concepts**: Core concept_id fields defining the clinical entity
- **Type Concepts**: _type_concept_id fields indicating data provenance

### Tier 2: High Priority Fields (≥90% Expected)
Essential for analytics and traceability:
- **Source Values**: _source_value fields for vocabulary mapping traceability
- **Visit Linkages**: visit_occurrence_id in clinical event tables
- **Standard Concepts**: Standard concept mappings where available

### Tier 3: Contextual Fields (≥70% Expected)
Important for analysis quality:
- **Provider Associations**: provider_id fields for clinical attribution
- **Care Site Linkages**: care_site_id for facility-based analysis
- **Clinical Context**: Status, modifier, and qualifier fields

## Table-Specific Requirements

### Person-Level Tables

#### PERSON (Foundation Table)
- **Required (100%)**: person_id, gender_concept_id, year_of_birth, race_concept_id, ethnicity_concept_id
- **Expected (≥80%)**: month/day of birth when available
- **Geographic (≥70%)**: location_id for population analysis

#### OBSERVATION_PERIOD (Coverage Table)
- **Required (100%)**: All fields mandatory
- **Coverage Rule**: Every person must have ≥1 observation period
- **Temporal Rule**: Periods cannot overlap, gaps indicate inactive observation

### Clinical Event Tables

#### VISIT_OCCURRENCE (Encounter Foundation)
- **Required (100%)**: visit_occurrence_id, person_id, visit_concept_id, visit_start_date, visit_end_date, visit_type_concept_id
- **High Priority (≥80%)**: care_site_id, visit_source_value
- **Contextual (≥70%)**: provider_id, admission/discharge concepts for inpatient visits

#### CONDITION_OCCURRENCE (Diagnosis Recording)
- **Required (100%)**: condition_occurrence_id, person_id, condition_concept_id, condition_start_date, condition_type_concept_id
- **High Priority (≥85%)**: visit_occurrence_id for encounter linkage
- **Traceability (≥95%)**: condition_source_value for source code preservation

#### DRUG_EXPOSURE (Medication Recording)  
- **Required (100%)**: drug_exposure_id, person_id, drug_concept_id, drug_exposure_start_date, drug_exposure_end_date, drug_type_concept_id
- **Clinical Context (≥70%)**: quantity, days_supply for dosing analysis
- **Traceability (≥95%)**: drug_source_value for source code preservation

#### PROCEDURE_OCCURRENCE (Procedure Recording)
- **Required (100%)**: procedure_occurrence_id, person_id, procedure_concept_id, procedure_date, procedure_type_concept_id  
- **High Priority (≥85%)**: visit_occurrence_id for encounter linkage
- **Traceability (≥95%)**: procedure_source_value for source code preservation

#### MEASUREMENT (Lab/Vital Recording)
- **Required (100%)**: measurement_id, person_id, measurement_concept_id, measurement_date, measurement_type_concept_id
- **Value Rules**: At least one of value_as_number, value_as_concept_id, or value_as_string must be populated (≥85%)
- **Unit Rule**: unit_concept_id required when value_as_number is populated (≥95%)

## Vocabulary and Reference Standards

### CONCEPT Table Requirements
- **Complete Coverage**: All concept_ids referenced in clinical tables must exist
- **Standard Designation**: standard_concept field must be properly populated ('S', 'C', or NULL)
- **Temporal Validity**: valid_start_date ≤ valid_end_date
- **Domain Consistency**: domain_id must align with usage in clinical tables

### Source-to-Standard Mapping
- **Mapping Coverage**: ≥90% of source codes should have standard concept mappings
- **Relationship Integrity**: CONCEPT_RELATIONSHIP table must support all mappings
- **Source Preservation**: Original source codes must be retained in _source_value fields

## Implementation Guidelines

### Pre-Implementation Assessment
1. **Source Data Audit**: Evaluate completeness of source system data
2. **Gap Analysis**: Identify fields that cannot meet required thresholds
3. **Mitigation Strategy**: Plan use of default/unknown concepts for unavoidable gaps
4. **Vocabulary Preparation**: Ensure complete vocabulary coverage for source codes

### ETL Quality Controls  
1. **Required Field Validation**: 100% completeness checks for Tier 1 fields
2. **Threshold Monitoring**: Automated alerts when fields fall below expected thresholds
3. **Referential Integrity**: Foreign key validation across all tables
4. **Temporal Consistency**: Date logic validation and observation period alignment

### Post-Implementation Monitoring
1. **DQD Execution**: Regular Data Quality Dashboard runs with threshold reporting
2. **Trend Analysis**: Monitor completeness trends over time
3. **Source Feedback**: Provide completeness reports to source system owners
4. **Continuous Improvement**: Iterative enhancement based on quality findings

## Quality Benchmarks

### Acceptable Implementation Standards
- **Clinical Core Tables**: 85-95% overall completeness score
- **Required Fields**: 100% completeness (no exceptions)
- **High Priority Fields**: ≥90% completeness  
- **Vocabulary Coverage**: ≥90% of source codes mapped to standard concepts

### Quality Improvement Targets
- **Source Value Preservation**: ≥95% for all _source_value fields
- **Visit Linkage**: ≥85% of clinical events linked to visits
- **Provider Attribution**: ≥70% of clinical events attributed to providers
- **Temporal Precision**: ≥80% of events with precise date/time information

## Common Implementation Challenges

### Data Availability Issues
- **Missing Demographics**: Use OMOP Unknown concepts (8552, 8551) rather than NULL
- **Incomplete Provider Data**: Populate with concept 0 (No matching concept) when unavoidable
- **Date Precision**: Accept date-only when date-time unavailable, but prefer precision

### Vocabulary Mapping Gaps
- **Local Code Systems**: Create custom vocabulary entries for unmapped source codes
- **Deprecated Concepts**: Use CONCEPT_RELATIONSHIP to map to current standard concepts
- **Domain Misalignment**: Review and correct concept domain assignments during mapping

### Performance Considerations
- **Large Dataset Processing**: Implement incremental completeness checking
- **Real-time Monitoring**: Use efficient queries for continuous quality assessment
- **Resource Optimization**: Balance completeness checking frequency with system performance

## References and Tools

### Primary Sources
- [OHDSI Data Quality Dashboard](https://github.com/OHDSI/DataQualityDashboard) - Automated quality checking framework
- [THEMIS Conventions](https://ohdsi.github.io/Themis/) - Community-ratified implementation standards  
- [OMOP CDM v5.4 Specifications](https://ohdsi.github.io/CommonDataModel/cdm54.html) - Official table definitions
- [OHDSI Collaborative](https://www.ohdsi.org/) - Community best practices and support

### Implementation Tools
- **DataQualityDashboard R Package**: Automated completeness assessment
- **ACHILLES**: Data characterization and profiling
- **ATLAS**: Cohort definition and quality review interface
- **SQLRender**: Cross-platform SQL query generation for quality checks

### Configuration Files
- `OMOP_CDMv5.4_Field_Level.csv` - Official DQD field-level thresholds
- `OMOP_CDMv5.4_Table_Level.csv` - Table-level completeness requirements  
- `OMOP_CDMv5.4_Concept_Level.csv` - Concept usage validation rules

---

**Document Version**: 1.0  
**Last Updated**: December 2024  
**Based on**: OMOP CDM v5.4, OHDSI DQD v2.6+, THEMIS v1.0+
