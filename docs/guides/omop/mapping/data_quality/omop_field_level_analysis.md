# OMOP CDM v5.4 Field-Level Completeness Analysis

## Overview

This document provides a detailed analysis of OMOP CDM v5.4 field-level completeness requirements based on the official OHDSI Data Quality Dashboard field-level configuration file (`OMOP_CDMv5.4_Field_Level.csv`).

## Analysis Methodology

The analysis is based on the official OHDSI Data Quality Dashboard configuration files that define:
- **isRequired** flags for mandatory fields (threshold typically 0%)
- **Completeness check configurations** for all CDM tables and fields
- **Foreign key validation** settings
- **Standard concept validation** requirements
- **Plausible value ranges** and temporal checks

## Core Clinical Tables Analysis

### PERSON Table Requirements

| Field | Required | DQD Threshold | Notes |
|-------|----------|---------------|-------|
| `person_id` | Yes | 0% | Primary key - must be unique and not null |
| `gender_concept_id` | Yes | 0% | Standard concept from Gender domain |
| `year_of_birth` | Yes | 0% | Four-digit year |
| `race_concept_id` | Yes | 0% | Standard concept from Race domain |
| `ethnicity_concept_id` | Yes | 0% | Standard concept from Ethnicity domain |
| `month_of_birth` | No | 20% | Optional but recommended when available |
| `day_of_birth` | No | 20% | Optional but recommended when available |
| `birth_datetime` | No | 20% | Optional precision enhancement |
| `location_id` | No | 30% | Geographic reference |
| `provider_id` | No | 50% | Primary care provider |
| `care_site_id` | No | 50% | Primary care site |

**THEMIS Convention Notes:**
- Gender must use standard concepts (8507=MALE, 8532=FEMALE, 8551=UNKNOWN)
- Year of birth must be reasonable (typically 1900-current year)
- Unknown values should use appropriate OMOP concepts rather than NULL

### OBSERVATION_PERIOD Table Requirements

| Field | Required | DQD Threshold | Notes |
|-------|----------|---------------|-------|
| `observation_period_id` | Yes | 0% | Primary key |
| `person_id` | Yes | 0% | Foreign key to PERSON |
| `observation_period_start_date` | Yes | 0% | Valid date |
| `observation_period_end_date` | Yes | 0% | Valid date >= start_date |
| `period_type_concept_id` | Yes | 0% | Standard concept from Period Type domain |

**Key Requirements:**
- Every person must have at least one observation period
- Observation periods cannot overlap for the same person
- End date must be >= start date

### VISIT_OCCURRENCE Table Requirements

| Field | Required | DQD Threshold | Notes |
|-------|----------|---------------|-------|
| `visit_occurrence_id` | Yes | 0% | Primary key |
| `person_id` | Yes | 0% | Foreign key to PERSON |
| `visit_concept_id` | Yes | 0% | Standard concept from Visit domain |
| `visit_start_date` | Yes | 0% | Valid date |
| `visit_end_date` | Yes | 0% | Valid date >= start_date |
| `visit_type_concept_id` | Yes | 0% | Standard concept from Visit Type domain |
| `provider_id` | No | 30% | Attending provider |
| `care_site_id` | No | 20% | Facility location |
| `visit_source_value` | No | 10% | Source system identifier |
| `visit_source_concept_id` | No | 15% | Source concept mapping |
| `admitted_from_concept_id` | No | 40% | For inpatient visits |
| `discharge_to_concept_id` | No | 40% | For inpatient visits |

**Visit Concept Standards:**
- 9201=Inpatient Visit, 9202=Outpatient Visit, 9203=Emergency Room Visit
- 262=Emergency Room and Inpatient Visit, 8971=Intensive Care

### CONDITION_OCCURRENCE Table Requirements

| Field | Required | DQD Threshold | Notes |
|-------|----------|---------------|-------|
| `condition_occurrence_id` | Yes | 0% | Primary key |
| `person_id` | Yes | 0% | Foreign key to PERSON |
| `condition_concept_id` | Yes | 0% | Standard concept from Condition domain |
| `condition_start_date` | Yes | 0% | Valid date |
| `condition_type_concept_id` | Yes | 0% | Standard concept from Condition Type domain |
| `condition_end_date` | No | 30% | When condition resolved |
| `provider_id` | No | 30% | Diagnosing provider |
| `visit_occurrence_id` | No | 15% | Associated visit |
| `condition_source_value` | No | 5% | Source diagnosis code |
| `condition_source_concept_id` | No | 10% | Source concept mapping |
| `condition_status_concept_id` | No | 40% | Primary/secondary designation |

**Condition Type Standards:**
- 32020=EHR encounter diagnosis, 32019=EHR billing diagnosis
- 32817=EHR Chief Complaint, 38000276=Clinical Study

### DRUG_EXPOSURE Table Requirements

| Field | Required | DQD Threshold | Notes |
|-------|----------|---------------|-------|
| `drug_exposure_id` | Yes | 0% | Primary key |
| `person_id` | Yes | 0% | Foreign key to PERSON |
| `drug_concept_id` | Yes | 0% | Standard concept from Drug domain |
| `drug_exposure_start_date` | Yes | 0% | Valid date |
| `drug_exposure_end_date` | Yes | 0% | Valid date >= start_date |
| `drug_type_concept_id` | Yes | 0% | Standard concept from Drug Type domain |
| `stop_reason` | No | 60% | Reason for discontinuation |
| `refills` | No | 25% | Number of refills |
| `quantity` | No | 25% | Quantity dispensed |
| `days_supply` | No | 25% | Days of therapy |
| `sig` | No | 50% | Prescription instructions |
| `route_concept_id` | No | 30% | Route of administration |
| `lot_number` | No | 80% | Manufacturing lot |
| `provider_id` | No | 30% | Prescribing provider |
| `visit_occurrence_id` | No | 20% | Associated visit |
| `drug_source_value` | No | 5% | Source medication code |

**Drug Type Standards:**
- 38000177=Prescription dispensed in pharmacy
- 38000175=Prescription dispensed through mail order
- 581373=Physician administered drug

### PROCEDURE_OCCURRENCE Table Requirements

| Field | Required | DQD Threshold | Notes |
|-------|----------|---------------|-------|
| `procedure_occurrence_id` | Yes | 0% | Primary key |
| `person_id` | Yes | 0% | Foreign key to PERSON |
| `procedure_concept_id` | Yes | 0% | Standard concept from Procedure domain |
| `procedure_date` | Yes | 0% | Valid date |
| `procedure_type_concept_id` | Yes | 0% | Standard concept from Procedure Type domain |
| `modifier_concept_id` | No | 40% | Procedure modifier |
| `quantity` | No | 50% | Number of procedures |
| `provider_id` | No | 30% | Performing provider |
| `visit_occurrence_id` | No | 15% | Associated visit |
| `procedure_source_value` | No | 5% | Source procedure code |
| `procedure_source_concept_id` | No | 10% | Source concept mapping |
| `modifier_source_value` | No | 60% | Source modifier code |

**Procedure Type Standards:**
- 38000267=EHR billing code, 38000275=EHR order list entry
- 32818=EHR admission note, 32817=EHR chief complaint

### MEASUREMENT Table Requirements

| Field | Required | DQD Threshold | Notes |
|-------|----------|---------------|-------|
| `measurement_id` | Yes | 0% | Primary key |
| `person_id` | Yes | 0% | Foreign key to PERSON |
| `measurement_concept_id` | Yes | 0% | Standard concept from Measurement domain |
| `measurement_date` | Yes | 0% | Valid date |
| `measurement_type_concept_id` | Yes | 0% | Standard concept from Meas Type domain |
| `value_as_number` | No | 15% | Numeric measurement value |
| `value_as_concept_id` | No | 20% | Categorical measurement value |
| `unit_concept_id` | No | 5% | Measurement unit (required if value_as_number) |
| `range_low` | No | 60% | Normal range lower bound |
| `range_high` | No | 60% | Normal range upper bound |
| `provider_id` | No | 30% | Ordering provider |
| `visit_occurrence_id` | No | 20% | Associated visit |
| `measurement_source_value` | No | 5% | Source measurement code |

**THEMIS Convention:**
- VALUE_AS_NUMBER and VALUE_AS_CONCEPT_ID are not mutually exclusive
- At least one of VALUE_AS_NUMBER, VALUE_AS_CONCEPT_ID, or VALUE_AS_STRING should be populated

## Vocabulary Tables Requirements

### CONCEPT Table
- **concept_id**: Primary key, no duplicates, no nulls
- **concept_name**: Required, non-empty string
- **domain_id**: Required, valid domain
- **vocabulary_id**: Required, valid vocabulary
- **concept_class_id**: Required, valid concept class
- **standard_concept**: 'S' for standard, 'C' for classification, NULL for non-standard
- **concept_code**: Required, source vocabulary code
- **valid_start_date**: Required, valid date
- **valid_end_date**: Required, valid date >= valid_start_date

### CONCEPT_RELATIONSHIP Table
- **concept_id_1**: Required, valid concept_id
- **concept_id_2**: Required, valid concept_id
- **relationship_id**: Required, valid relationship
- **valid_start_date**: Required, valid date
- **valid_end_date**: Required, valid date >= valid_start_date

## Quality Thresholds Summary

### Critical Thresholds (Must be 100%)
- All primary keys
- All required foreign keys
- All required date fields
- All required concept_id fields

### High Priority Thresholds (≥90%)
- Source value fields for traceability
- Unit fields when numeric values present
- Visit linkages for encounter-based events

### Medium Priority Thresholds (≥70%)
- Provider associations
- Care site linkages
- Status and modifier fields

### Low Priority Thresholds (≥50%)
- Optional descriptive fields
- System-generated fields
- Complex derived fields

## Implementation Guidelines

### Data Quality Assessment Process
1. **Pre-ETL Validation**: Source data completeness assessment
2. **ETL Monitoring**: Real-time completeness tracking during transformation
3. **Post-ETL Validation**: OHDSI Data Quality Dashboard execution
4. **Continuous Monitoring**: Ongoing completeness trend analysis

### Acceptable Completeness Ranges
- **Clinical Core Tables**: 85-95% overall completeness
- **Reference Tables**: 95-100% completeness
- **Optional Fields**: 60-80% completeness acceptable
- **Required Fields**: 100% completeness mandatory

### Quality Improvement Process
1. **Gap Identification**: Use DQD results to identify completeness gaps
2. **Root Cause Analysis**: Investigate source system data quality
3. **ETL Enhancement**: Improve mapping and transformation logic
4. **Source System Feedback**: Work with data providers to improve capture
5. **Compensatory Measures**: Use default concepts for unavoidable gaps

## References

- OHDSI Data Quality Dashboard Field Level Configuration
- OMOP CDM v5.4 Specifications
- THEMIS Conventions for Field Population
- OHDSI Community Best Practices
