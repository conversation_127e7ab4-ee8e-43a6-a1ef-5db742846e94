# Abu Dhabi Claims to OMOP ETL Configuration
# Based on validated notebook implementation
# Author: FHIR-OMOP Development Team
# Created: December 2024

# Database Configuration
database:
  host: "localhost"
  port: 5432
  database: "omop_abu_dhabi"
  user: "jaime<PERSON>"
  password: ""  # Empty for local macOS setup
  schema: "public"

# Data Configuration
data:
  # Input data path
  input_csv: "data/real_test_datasets/claim_anonymized.csv"
  
  # Expected metrics from notebook validation
  expected_records: 3185
  expected_patients: 447
  expected_visits: 1201
  expected_providers: 171
  
  # Data validation tolerances
  tolerance_percent: 5  # Allow 5% variance in record counts

# Field Mappings (based on notebook transformations)
field_mappings:
  # Person table mappings
  person:
    source_id: "aio_patient_id"
    person_id: "aio_patient_id"
    person_source_value: "aio_patient_id"
    # Demographics not available in dataset
    gender_concept_id: 0  # Unknown
    race_concept_id: 0    # Unknown
    ethnicity_concept_id: 0  # Unknown
  
  # Visit occurrence mappings
  visit_occurrence:
    visit_occurrence_id: "case"
    person_id: "aio_patient_id"
    visit_start_date: "encounter_start_date"
    visit_end_date: "encounter_end_date"
    visit_concept_id: 9202  # Outpatient Visit
    visit_type_concept_id: 44818517  # EHR encounter record
    provider_id: "provider_id"
    care_site_id: "receiver_id"
    visit_source_value: "case_type"
    admitting_source_value: "encounter_start_type_desc"
  
  # Procedure occurrence mappings
  procedure_occurrence:
    procedure_occurrence_id: "activity_id"
    person_id: "aio_patient_id"
    visit_occurrence_id: "case"
    procedure_date: "start_activity_date"  # fallback: encounter_start_date
    procedure_concept_id: 0  # Will map CPT codes later
    procedure_type_concept_id: 38000275  # EHR order list
    quantity: "activity_quantity"
    provider_id: "clinician"
    procedure_source_value: "code_activity"  # CPT code
    modifier_source_value: "act_type_desc"
  
  # Cost table mappings
  cost:
    cost_id: "activity_id"
    cost_event_id: "activity_id"
    cost_domain_id: "Procedure"
    cost_type_concept_id: 5032  # Claim
    currency_concept_id: 44818568  # AED currency
    total_charge: "gross"
    total_cost: "net"
    total_paid: "payment_amount"
    paid_by_patient: "patient_share"
    payer_plan_period_id: "insurance_plan_id"
    amount_allowed: "net"
    revenue_code_source_value: "type_activity"
  
  # Provider table mappings
  provider:
    provider_id: "clinician"
    provider_name: "clinician_name"
    care_site_id: "institution_name"
    provider_source_value: "clinician"
    # Provider demographics not available
    specialty_concept_id: 0  # Unknown
    gender_concept_id: 0     # Unknown

# Processing Configuration
processing:
  # CPT code filtering (based on notebook logic)
  cpt_filter_column: "act_type_desc"
  cpt_filter_value: "CPT"
  
  # Batch processing settings
  batch_size: 1000
  
  # Date format for parsing
  date_format: "%d/%m/%Y"
  
  # Memory optimization
  chunk_size: 10000  # For large datasets

# Validation Configuration
validation:
  # Required columns for processing
  required_columns:
    - "aio_patient_id"
    - "case"
    - "activity_id"
    - "code_activity"
    - "clinician"
    - "encounter_start_date"
    - "encounter_end_date"
    - "gross"
    - "net"
    - "patient_share"
    - "payment_amount"
  
  # Data quality checks
  quality_checks:
    min_records: 1000
    min_patients: 100
    min_visits: 100
    min_providers: 10
  
  # Referential integrity checks
  integrity_checks:
    - "person_id exists in person table"
    - "visit_occurrence_id exists in visit_occurrence table"
    - "provider_id exists in provider table"

# Logging Configuration
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  
  # Log file settings
  file_logging:
    enabled: false
    filename: "abu_dhabi_etl.log"
    max_size_mb: 10
    backup_count: 5

# Output Configuration
output:
  # Intermediate file outputs
  save_intermediate: false
  intermediate_dir: "temp/abu_dhabi_etl"
  
  # Validation reports
  generate_reports: true
  reports_dir: "reports/abu_dhabi_etl"
  
  # Export formats
  export_formats:
    - "csv"
    - "json"

# Performance Configuration
performance:
  # Connection pooling
  pool_size: 5
  max_overflow: 10
  
  # Query optimization
  use_bulk_insert: true
  commit_frequency: 1000
  
  # Memory management
  max_memory_mb: 2048

# Environment-specific overrides
environments:
  development:
    database:
      host: "localhost"
      port: 5432
    logging:
      level: "DEBUG"
  
  testing:
    database:
      database: "omop_abu_dhabi_test"
    validation:
      quality_checks:
        min_records: 100
  
  production:
    database:
      host: "production-db-host"
      port: 5432
    logging:
      level: "WARNING"
      file_logging:
        enabled: true
    performance:
      pool_size: 20
      max_overflow: 30
