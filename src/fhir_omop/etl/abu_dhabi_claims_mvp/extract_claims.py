"""
Abu Dhabi Claims Data Extraction Module

This module extracts and validates claims data from CSV files for OMOP transformation.
Based on validated logic from learning_notes/omop_learning_notebook.ipynb.

Author: FHIR-OMOP Development Team
Created: December 2024
License: MIT
"""

import os
import argparse
import logging
from typing import Optional
from pathlib import Path

import pandas as pd
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def parse_args():
    """Parse command line arguments.
    
    Returns
    -------
    argparse.Namespace
        Parsed command line arguments
    """
    parser = argparse.ArgumentParser(
        description="Extract Abu Dhabi claims data from CSV file"
    )
    parser.add_argument(
        "--input-file",
        default=os.getenv("INPUT_FILE", "data/real_test_datasets/claim_anonymized.csv"),
        help="Path to the input CSV file"
    )
    parser.add_argument(
        "--output-file", 
        default=os.getenv("OUTPUT_FILE"),
        help="Path to save extracted data (optional)"
    )
    parser.add_argument(
        "--validate-only",
        action="store_true",
        help="Only validate data without saving"
    )
    return parser.parse_args()


def extract_claims_data(csv_path: str) -> pd.DataFrame:
    """Extract claims data from CSV file.
    
    Based on notebook validation with 3,185 records.
    Implements the exact logic from notebook cells 3-4.
    
    Parameters
    ----------
    csv_path : str
        Path to the CSV file containing claims data
        
    Returns
    -------
    pd.DataFrame
        DataFrame with validated claims data
        
    Raises
    ------
    FileNotFoundError
        If the CSV file is not found
    ValueError
        If the CSV file is empty or has invalid format
    """
    logger.info(f"Extracting claims data from: {csv_path}")
    
    # Validate file exists
    if not Path(csv_path).exists():
        raise FileNotFoundError(f"CSV file not found: {csv_path}")
    
    try:
        # Load CSV data - based on notebook cell 3
        claims_df = pd.read_csv(csv_path)
        logger.info(f"Loaded {len(claims_df):,} raw records from CSV")
        
        # Validate basic structure
        if claims_df.empty:
            raise ValueError("CSV file is empty")
            
        # Log basic info about the dataset
        logger.info(f"Dataset shape: {claims_df.shape}")
        logger.info(f"Columns: {list(claims_df.columns)}")
        
        return claims_df
        
    except pd.errors.EmptyDataError:
        raise ValueError("CSV file is empty or has no data")
    except pd.errors.ParserError as e:
        raise ValueError(f"Error parsing CSV file: {e}")
    except Exception as e:
        raise ValueError(f"Unexpected error reading CSV: {e}")


def filter_cpt_procedures(claims_df: pd.DataFrame) -> pd.DataFrame:
    """Filter claims data to include only CPT procedures.
    
    Based on notebook validation - filters for 5-digit CPT codes.
    Implements the exact logic from notebook cell 4.
    
    Parameters
    ----------
    claims_df : pd.DataFrame
        Raw claims data
        
    Returns
    -------
    pd.DataFrame
        Filtered data containing only CPT procedures
    """
    logger.info("Filtering for CPT procedures (5-digit codes)")
    
    # Check if required column exists
    if 'code_activity' not in claims_df.columns:
        raise ValueError("Required column 'code_activity' not found in data")
    
    # Filter for CPT codes - based on notebook cell 4
    # CPT codes are 5-digit numeric codes
    cpt_mask = claims_df['code_activity'].str.match(r'^\d{5}$', na=False)
    cpt_data = claims_df[cpt_mask].copy()
    
    logger.info(f"Filtered to {len(cpt_data):,} CPT procedure records")
    logger.info(f"Filtered out {len(claims_df) - len(cpt_data):,} non-CPT records")
    
    # Validate we have data after filtering
    if cpt_data.empty:
        logger.warning("No CPT procedures found in the dataset")
    
    return cpt_data


def validate_extracted_data(data: pd.DataFrame) -> dict:
    """Validate extracted data against expected metrics.
    
    Based on notebook validation results:
    - Expected: 3,185 CPT procedure records
    - Expected: 447 unique patients
    - Expected: 1,201 unique visits
    
    Parameters
    ----------
    data : pd.DataFrame
        Extracted and filtered claims data
        
    Returns
    -------
    dict
        Validation results with metrics
    """
    logger.info("Validating extracted data against expected metrics")
    
    # Calculate key metrics
    total_records = len(data)
    unique_patients = data['aio_patient_id'].nunique() if 'aio_patient_id' in data.columns else 0
    unique_visits = data['case'].nunique() if 'case' in data.columns else 0
    unique_providers = data['clinician'].nunique() if 'clinician' in data.columns else 0
    
    # Expected values from notebook validation
    expected_records = 3185
    expected_patients = 447
    expected_visits = 1201
    expected_providers = 171
    
    validation_results = {
        'total_records': total_records,
        'unique_patients': unique_patients,
        'unique_visits': unique_visits,
        'unique_providers': unique_providers,
        'expected_records': expected_records,
        'expected_patients': expected_patients,
        'expected_visits': expected_visits,
        'expected_providers': expected_providers,
        'records_match': total_records == expected_records,
        'patients_match': unique_patients == expected_patients,
        'visits_match': unique_visits == expected_visits,
        'providers_match': unique_providers == expected_providers
    }
    
    # Log validation results
    logger.info("Validation Results:")
    logger.info(f"  Records: {total_records:,} (expected: {expected_records:,}) ✅" if validation_results['records_match'] else f"  Records: {total_records:,} (expected: {expected_records:,}) ❌")
    logger.info(f"  Patients: {unique_patients:,} (expected: {expected_patients:,}) ✅" if validation_results['patients_match'] else f"  Patients: {unique_patients:,} (expected: {expected_patients:,}) ❌")
    logger.info(f"  Visits: {unique_visits:,} (expected: {expected_visits:,}) ✅" if validation_results['visits_match'] else f"  Visits: {unique_visits:,} (expected: {expected_visits:,}) ❌")
    logger.info(f"  Providers: {unique_providers:,} (expected: {expected_providers:,}) ✅" if validation_results['providers_match'] else f"  Providers: {unique_providers:,} (expected: {expected_providers:,}) ❌")
    
    return validation_results


def main():
    """Main execution function.
    
    Extracts and validates Abu Dhabi claims data from CSV file.
    Reproduces notebook extraction logic in automated fashion.
    """
    args = parse_args()
    
    try:
        # Extract raw claims data
        claims_data = extract_claims_data(args.input_file)
        
        # Filter for CPT procedures
        cpt_data = filter_cpt_procedures(claims_data)
        
        # Validate extracted data
        validation_results = validate_extracted_data(cpt_data)
        
        # Save extracted data if output file specified
        if args.output_file and not args.validate_only:
            output_path = Path(args.output_file)
            output_path.parent.mkdir(parents=True, exist_ok=True)
            cpt_data.to_csv(output_path, index=False)
            logger.info(f"Saved extracted data to: {output_path}")
        
        # Summary
        all_validations_pass = all([
            validation_results['records_match'],
            validation_results['patients_match'], 
            validation_results['visits_match'],
            validation_results['providers_match']
        ])
        
        if all_validations_pass:
            logger.info("✅ All validations passed - data extraction successful")
        else:
            logger.warning("⚠️ Some validations failed - please review data")
            
        return cpt_data
        
    except Exception as e:
        logger.error(f"Error during data extraction: {e}")
        raise


if __name__ == "__main__":
    main()
