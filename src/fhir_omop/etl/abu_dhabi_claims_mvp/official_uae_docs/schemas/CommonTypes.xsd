<!--Created with Liquid XML Studio 1.0.8.0 (http://www.liquid-technologies.com)-->
<xs:schema elementFormDefault="qualified" targetNamespace="http://www.haad.ae/DataDictionary/CommonTypes" version="2.0" id="CommonTypes" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:xsd="undefined">
   <xs:simpleType name="HeaderSenderID">
      <xs:annotation>
         <xs:documentation>License number of the Provider, Insurer or TPA sending information. For transaction pairs the receiver of the first transaction must be the sender of the second transaction, e.g., if a TPA receives a ClaimSubmission from a provider, then that TPA (not the insurer) must send the RemittanceAdvice to the provider. Lists of valid license numbers are available on www.shafafiya.org/dictionary >> Licenses.</xs:documentation>
      </xs:annotation>
      <xs:restriction base="xs:string">
         <xs:minLength value="1"/>
         <xs:whiteSpace value="collapse"/>
      </xs:restriction>
   </xs:simpleType>
   <xs:simpleType name="HeaderReceiverID">
      <xs:annotation>
         <xs:documentation>License number of the Provider, Insurer or TPA receiving information. For transaction pairs the receiver of the first transaction must be the sender of the second transaction, e.g., if a TPA receives a ClaimSubmission from a provider, then that TPA (not the insurer) must send the RemittanceAdvice to the provider. Lists of valid license numbers are available on www.shafafiya.org/dictionary >> Licenses.</xs:documentation>
      </xs:annotation>
      <xs:restriction base="xs:string">
         <xs:minLength value="1"/>
         <xs:whiteSpace value="collapse"/>
      </xs:restriction>
   </xs:simpleType>
   <xs:simpleType name="HeaderTransactionDate">
      <xs:annotation>
         <xs:documentation>System generated date and time specifying when the transaction was generated.</xs:documentation>
      </xs:annotation>
      <xs:restriction base="q1:DateTimeForm" xmlns:q1="http://www.haad.ae/DataDictionary/CommonTypes">
         <xs:minLength value="1"/>
         <xs:whiteSpace value="collapse"/>
      </xs:restriction>
   </xs:simpleType>
   <xs:simpleType name="HeaderRecordCount">
      <xs:annotation>
         <xs:documentation>The number of records contained in the XML document at the highest level.
		Examples: The number of Person elements in the PersonRegister file. The number of Claim elements in the ClaimSubmission file.</xs:documentation>
      </xs:annotation>
      <xs:restriction base="xs:nonNegativeInteger"/>
   </xs:simpleType>
   <xs:simpleType name="HeaderDispositionFlag">
      <xs:annotation>
         <xs:documentation>Flag to determine whether the submission file is sent to the receiver or only checked against the validation rules. 
 
The following values are allowed in the production environment:
-‘PRODUCTION’ - upon successful validation the transaction file is saved in the Post Office and made available for download by the receiver
-‘TEST’ - the validation engine checks the data in the submission against all production-version validation rules and provides an error report to the user; the transaction file is immediately discarded without being sent to the receiver
 
In the Public Test Environment the following values are allowed:
-'PTE_SUBMIT' - upon successful validation the transaction file is saved in the Post Office and made available for download by the receiver
-‘PTE_VALIDATE_ONLY’ - the validation engine checks the data in the submission against all validation rules in the PTE version and provides an error report to the user; the transaction file is immediately discarded without being sent to the receiver
-‘PTE_RESPONSE’ - upon successful validation the transaction file is saved in the Post Office, and auto-reply is generated if uploaded transaction assumes the reply
 
The rules for the Public Test Environment are described at www.haad.ae/datadictionary.</xs:documentation>
      </xs:annotation>
      <xs:restriction base="xs:string">
         <xs:enumeration value="PRODUCTION"/>
         <xs:enumeration value="TEST"/>
         <xs:enumeration value="PTE_SUBMIT"/>
         <xs:enumeration value="PTE_VALIDATE_ONLY"/>
         <xs:enumeration value="PTE_RESPONSE"/>
         <xs:enumeration value="PTE_SHADOW_NOT_FOR_PAYMENT_SUBMIT"/>
         <xs:enumeration value="PTE_SHADOW_NOT_FOR_PAYMENT_VALIDATE_ONLY"/>
         <xs:enumeration value="SHADOW_NOT_FOR_PAYMENT_SUBMIT"/>
         <xs:enumeration value="SHADOW_NOT_FOR_PAYMENT_VALIDATE_ONLY"/>
         <xs:minLength value="1"/>
         <xs:whiteSpace value="collapse"/>
      </xs:restriction>
   </xs:simpleType>
   <xs:simpleType name="TimeForm">
      <xs:annotation>
         <xs:documentation>Time data type enforcing the format: "HH:MM".</xs:documentation>
      </xs:annotation>
      <xs:restriction base="xs:string">
         <xs:pattern value="(20|21|22|23|[0-1]?\d):[0-5]?\d"/>
      </xs:restriction>
   </xs:simpleType>
   <xs:simpleType name="DateForm">
      <xs:annotation>
         <xs:documentation>Date data type enforcing the format: "dd/mm/yyyy".</xs:documentation>
      </xs:annotation>
      <xs:restriction base="xs:string">
         <xs:minLength value="0"/>
         <xs:pattern value="\d{2}/\d{2}/\d{4}"/>
      </xs:restriction>
   </xs:simpleType>
   <xs:simpleType name="DateTimeForm">
      <xs:annotation>
         <xs:documentation>Date + Time data type enforcing the format: "dd/mm/yyyy HH:MM"", time synchronised with time-a.nist.gov, time zone GMT+4 with no winter time adjustment.</xs:documentation>
      </xs:annotation>
      <xs:restriction base="xs:string">
         <xs:minLength value="0"/>
         <xs:pattern value="\d{2}/\d{2}/\d{4} (20|21|22|23|[0-1]?\d):[0-5]?\d"/>
      </xs:restriction>
   </xs:simpleType>
   <xs:simpleType name="MemberID">
      <xs:annotation>
         <xs:documentation>Insurer's identifier for its member. The same MemberID cannot be assigned to more than one person. In PersonRegister transactions submitted by Providers, such as to report self pay, MemberID must be equal to ClaimMemberID reported in related claims (EncounterFacilityID#EncounterPatientID).</xs:documentation>
      </xs:annotation>
      <xs:restriction base="xs:string">
         <xs:minLength value="1"/>
         <xs:whiteSpace value="collapse"/>
      </xs:restriction>
   </xs:simpleType>
   <xs:simpleType name="MemberRelation">
      <xs:annotation>
         <xs:documentation>The information about the family relationships: Principal, Spouse, Child, Parent, Other.</xs:documentation>
      </xs:annotation>
      <xs:restriction base="xs:string">
         <xs:enumeration value="Principal"/>
         <xs:enumeration value="Spouse"/>
         <xs:enumeration value="Parent"/>
         <xs:enumeration value="Child"/>
         <xs:enumeration value="Other"/>
      </xs:restriction>
   </xs:simpleType>
   <xs:simpleType name="MemberRelationTo">
      <xs:annotation>
         <xs:documentation>The information about the MemberID of the principal member of the family.</xs:documentation>
      </xs:annotation>
      <xs:restriction base="xs:string"/>
   </xs:simpleType>
   <xs:simpleType name="PersonUIDNumber">
      <xs:annotation>
         <xs:documentation>The person's Unified Number issued by the MOI, uniquely identifies an individual through passport (for nationals) or visa (for expatriates) issuance and renewal processes. This identifier remains the same for the same person.</xs:documentation>
      </xs:annotation>
      <xs:restriction base="xs:string"/>
   </xs:simpleType>
   <xs:simpleType name="PersonVisaFileNumber">
      <xs:annotation>
         <xs:documentation>The person's visa file number issued by the MOI each time a visa (of whatever type) is issued, identified by UID. A person with a single UID can have multiple visa file numbers. This number is required for linking insurance status information with particular residence visa issue or renewal. In case of earlier residents from Dubai now on residence visa in Abu Dhabi, the UID number is not printed on either the visa or the residence permit, and therefore the visa file number is the only identifier available to pass on to the MOI.</xs:documentation>
      </xs:annotation>
      <xs:restriction base="xs:string"/>
   </xs:simpleType>
   <xs:simpleType name="PersonFirstName">
      <xs:annotation>
         <xs:documentation>The patient’s first name, as spelled in the passport.</xs:documentation>
      </xs:annotation>
      <xs:restriction base="xs:string"/>
   </xs:simpleType>
   <xs:simpleType name="PersonContactNumber">
      <xs:annotation>
         <xs:documentation>This is the telephone contact number provided by the patient. If multiple numbers are available, the mobile phone number should be used. 
	  If multiple mobile phone numbers are provided, it should be the first mentioned number, which is personal to the patient.</xs:documentation>
      </xs:annotation>
      <xs:restriction base="xs:string"/>
   </xs:simpleType>
   <xs:simpleType name="PersonBirthDate">
      <xs:annotation>
         <xs:documentation>Is the date on which a person was born or is officially deemed to have been born.
In cases, where despite best efforts PersonBirthDate is not known, but the age is known; then the birth date should be assumed to be on the 1st of January of the current year, minus the age of the person
Example | A patient arrives on January 8th 2008 and claims he is 64 years old, but does not know his date of birth. The PatientBirthDate should be assumed to be 01/01/1944..</xs:documentation>
      </xs:annotation>
      <xs:restriction base="q1:DateForm" xmlns:q1="http://www.haad.ae/DataDictionary/CommonTypes"/>
   </xs:simpleType>
   <xs:simpleType name="PersonGender">
      <xs:annotation>
         <xs:documentation>The patient’s gender

Restrictions:
Only values allowed are
1 = male
0 = female
9 = unknown</xs:documentation>
      </xs:annotation>
      <xs:restriction base="xs:integer">
         <xs:enumeration value="1"/>
         <xs:enumeration value="0"/>
         <xs:enumeration value="9"/>
      </xs:restriction>
   </xs:simpleType>
   <xs:simpleType name="PersonNationality">
      <xs:annotation>
         <xs:documentation>The current nationality of the person, as defined by the passport. Restrictions Only values from the reference list of nationalities are allowed. The latest List of Nationalities can be downloaded from www.shafafiya.org/dictionary >> Codes >> Other Codes.</xs:documentation>
      </xs:annotation>
      <xs:restriction base="xs:string">
         <xs:minLength value="1"/>
         <xs:whiteSpace value="collapse"/>
      </xs:restriction>
   </xs:simpleType>
   <xs:simpleType name="PersonCity">
      <xs:annotation>
         <xs:documentation>The person’s actual city of residence.</xs:documentation>
      </xs:annotation>
      <xs:restriction base="xs:string">
         <xs:minLength value="1"/>
         <xs:whiteSpace value="collapse"/>
      </xs:restriction>
   </xs:simpleType>
   <xs:simpleType name="PersonPassportNumber">
      <xs:annotation>
         <xs:documentation>The passport number, or if not available, the National ID.</xs:documentation>
      </xs:annotation>
      <xs:restriction base="xs:string"/>
   </xs:simpleType>
   <xs:simpleType name="PersonEmiratesIDNumber">
      <xs:annotation>
         <xs:documentation>The unique number the government assigns to a citizen. When an EmiratesIDNumber is not available :
      000-0000-0000000-0 National without card
      111-1111-1111111-1 Expatriate resident without a card
      222-2222-2222222-2 Non national, non-expat resident without a card
      999-9999-9999999-9 Unknown status, without a card.</xs:documentation>
      </xs:annotation>
      <xs:restriction base="xs:string">
         <xs:minLength value="1"/>
         <xs:whiteSpace value="collapse"/>
      </xs:restriction>
   </xs:simpleType>
   <xs:simpleType name="ContractPayerID">
      <xs:annotation>
         <xs:documentation>Insurer's license number the member has contract with.</xs:documentation>
      </xs:annotation>
      <xs:restriction base="xs:string"/>
   </xs:simpleType>
   <xs:simpleType name="ContractTPAID">
      <xs:annotation>
         <xs:documentation>License number of TPA that administers the contact the member has contract with.</xs:documentation>
      </xs:annotation>
      <xs:restriction base="xs:string"/>
   </xs:simpleType>
   <xs:simpleType name="ContractPackageName">
      <xs:annotation>
         <xs:documentation>This is the name of the insurance package taken from 'Package Name' column in the  list of authorized benefit packages available on www.shafafiya.org/dictionary >> Codes >> Benefit Packages.</xs:documentation>
      </xs:annotation>
      <xs:restriction base="xs:string"/>
   </xs:simpleType>
   <xs:simpleType name="ContractStartDate">
      <xs:annotation>
         <xs:documentation>This is the date the member first became insured.

Restrictions:
• The ContractStartDate can not be a future date
• The ContractStartDate can not be less than 01/01/1900.</xs:documentation>
      </xs:annotation>
      <xs:restriction base="q1:DateForm" xmlns:q1="http://www.haad.ae/DataDictionary/CommonTypes"/>
   </xs:simpleType>
   <xs:simpleType name="ContractRenewalDate">
      <xs:annotation>
         <xs:documentation>This is the date the insurance was last renewed. If it is a first time insurance, the date should be the same as used for
ContractStartDate.

Restrictions:
• The ContractRenewalDate can not be a future date
• The ContractRenewalDate can not be less than 01/01/1900.</xs:documentation>
      </xs:annotation>
      <xs:restriction base="q2:DateForm" xmlns:q2="http://www.haad.ae/DataDictionary/CommonTypes"/>
   </xs:simpleType>
   <xs:simpleType name="ContractExpiryDate">
      <xs:annotation>
         <xs:documentation>This is the date the insurance will expire if it is not renewed.

Restrictions:
• The ContractExpiryDate can not be less than 01/01/1900.</xs:documentation>
      </xs:annotation>
      <xs:restriction base="q3:DateForm" xmlns:q3="http://www.haad.ae/DataDictionary/CommonTypes"/>
   </xs:simpleType>
   <xs:simpleType name="ContractGrossPremium">
      <xs:annotation>
         <xs:documentation>This is the AED amount the of the monthly, quarterly or yearly premium, the member has to pay for his insurance policy.
The insurer may choose to use the average gross premium for the specific package used.</xs:documentation>
      </xs:annotation>
      <xs:restriction base="xs:float"/>
   </xs:simpleType>
   <xs:simpleType name="ContractPolicyHolder">
      <xs:annotation>
         <xs:documentation>The indication of the policy holder.

Restrictions:
1 = Government
2 = Government related services
4 = Private Companies &lt; 1000 employees
6 = Private Companies > 1000 employees and &lt;= 50000 employees
7 = Private Companies > 50000 employees and &lt;= 100000 employees
8 = Private Companies > 100000 employees
9 = Embassy
10 = Individual
11 = SME
12 = Diplomat
13 = Dar Zayed
99 = Others</xs:documentation>
      </xs:annotation>
      <xs:restriction base="xs:integer">
         <xs:enumeration value="1"/>
         <xs:enumeration value="2"/>
         <xs:enumeration value="4"/>
         <xs:enumeration value="6"/>
         <xs:enumeration value="7"/>
         <xs:enumeration value="8"/>
         <xs:enumeration value="9"/>
         <xs:enumeration value="10"/>
         <xs:enumeration value="11"/>
         <xs:enumeration value="12"/>
         <xs:enumeration value="13"/>
         <xs:enumeration value="99"/>
      </xs:restriction>
   </xs:simpleType>
   <xs:simpleType name="ContractCompanyID">
      <xs:annotation>
         <xs:documentation>This is the trade license number of the member's company.</xs:documentation>
      </xs:annotation>
      <xs:restriction base="xs:string"/>
   </xs:simpleType>
   <xs:simpleType name="ClaimID">
      <xs:annotation>
         <xs:documentation>The unique number assigned by the health provider to identify the Claim. This is also known as the provider’s Claims reference number. It will be unique for each Claim.

If the patient is not insured and pays out of pocket, this will be the external invoice reference number.
If the patient is a National the Claimid correspond to 'ProFormaPayer'.</xs:documentation>
      </xs:annotation>
      <xs:restriction base="xs:string">
         <xs:minLength value="1"/>
         <xs:whiteSpace value="collapse"/>
      </xs:restriction>
   </xs:simpleType>
   <xs:simpleType name="ClaimIDPayer">
      <xs:annotation>
         <xs:documentation>The unique number assigned by an insurer to identify the Claim. It helps the provider and payer to locate the Claim.
For non-insured patients this field is empty.</xs:documentation>
      </xs:annotation>
      <xs:restriction base="xs:string"/>
   </xs:simpleType>
   <xs:simpleType name="ClaimMemberID">
      <xs:annotation>
         <xs:documentation>The patient’s insurance member number, if the patient is claiming insurance. Otherwise, must be equal to EncounterFacilityID#EncounterPatientID.</xs:documentation>
      </xs:annotation>
      <xs:restriction base="xs:string"/>
   </xs:simpleType>
   <xs:simpleType name="ClaimPayerID">
      <xs:annotation>
         <xs:documentation>If the patient is claiming insurance cover, this is HAAD’s insurance license number.
If the patient is claiming insurance from an insurance not licensed by HAAD, this should be “@” followed by the name of the Insurance.
If the patient is classified as Medical Tourist and is paying directly for services provided, ClaimPayerID should be “MedicalTourismSelfPay”.
If the patient is classified as Medical Tourist and is not paying directly for services provided, ClaimPayerID should be “MedicalTourismOther”.
If the patient is paying directly for services provided, this should be “SelfPay”.
If the patient neither claims insurance nor pays directly for services provided, this should be “ProFormaPayer”. 
Example | H018 Example | @Cigna Medical Example | SelfPay Example | ProFormaPayer Example | MedicalTourismSelfPay Example | MedicalTourismOther.</xs:documentation>
      </xs:annotation>
      <xs:restriction base="xs:string">
         <xs:minLength value="1"/>
         <xs:whiteSpace value="collapse"/>
      </xs:restriction>
   </xs:simpleType>
   <xs:simpleType name="ClaimProviderID">
      <xs:annotation>
         <xs:documentation>ClaimProviderID is the license number of the provider claiming from the Payer. This can be a facility or a clinician. Lists of valid license numbers are available on www.shafafiya.org/dictionary >> Licenses. If the provider has no valid license number, the provider should be “@” followed by the name of the provider. Note | ClaimProviderID is sometimes also known as the billing provider. In general, the facility that hosted the Encounter is also the one that claims from the payer. In these cases, ClaimProviderID is the same as EncounterFacilityID. However, under some circumstances, it is a different party that claims, e.g., a clinician or a different facility. Example | A hospital group has multiple licensed facilities. The hospital group centralizes billing and claims on the main site. In this case, an Encounter that occurred in a satellite facility (EncounterFacilityID = SatelliteSite) would be billed by the main site, i.e., ClaimProviderID = MainSite).</xs:documentation>
      </xs:annotation>
      <xs:restriction base="xs:string"/>
   </xs:simpleType>
   <xs:simpleType name="ClaimEmiratesIDNumber">
      <xs:annotation>
         <xs:documentation>The unique number the government assigns to a citizen. When an EmiratesIDNumber is not available :
      000-0000-0000000-0 National without card
      111-1111-1111111-1 Expatriate resident without a card
      222-2222-2222222-2 Non national, non-expat resident without a card
      999-9999-9999999-9 Unknown status, without a card.</xs:documentation>
      </xs:annotation>
      <xs:restriction base="xs:string">
         <xs:minLength value="1"/>
         <xs:whiteSpace value="collapse"/>
      </xs:restriction>
   </xs:simpleType>
   <xs:simpleType name="ClaimGross">
      <xs:annotation>
         <xs:documentation>Is the total AED amount of the charges included on the Claim.
ClaimGross includes any patient financial responsibility for the Claim, such as co-pays and deductibles, as well as charges made to other insurers for the Encounter(s) covered by the Claim.

The prices on which ClaimGross are based should reflect the general agreement between the payer and provider for the Claim items for insuree. 

Example 1 | A patient visits a clinic for a hip operation. The published list price is AED 8000. However, the insurer has negotiated with the provider a general discount of 10% on the published list price. ClaimGross is AED 7200.

Example 2 | A patient visits a clinic for a routine physical exam which costs AED 2000.The patient pays a co-pay of AED 250. ClaimGross is AED 2000.

Example 3 | A patient visits a clinic for a physical exam (AED 500) and an expensive diagnostic test (AED 1500) in one Encounter. The patient pays a co-pay of AED 250 and claims the diagnostic test from a supplementary insurance, because the primary insurance does not cover this diagnostic test. ClaimGross is AED 2000. 

Note | If the claimed amount is not in AED, then value should be converted to AED on the date of ClaimDateSubmission

Restrictions:
Non-negative and greater than or equal to ClaimPatientShare + ClaimNet.</xs:documentation>
      </xs:annotation>
      <xs:restriction base="xs:float"/>
   </xs:simpleType>
   <xs:simpleType name="ClaimPatientShare">
      <xs:annotation>
         <xs:documentation>The amount a patient owes a provider according to the terms of their insurance plan/product. If the patient has no insurance coverage for the visit, they are considered self-pay and liable for the entire amount, per their signed consent for treatment.</xs:documentation>
      </xs:annotation>
      <xs:restriction base="xs:float"/>
   </xs:simpleType>
   <xs:simpleType name="ClaimNet">
      <xs:annotation>
         <xs:documentation>The net charges included on the Claim. This is the amount the provider is expected to be paid.

Example | A patient is admitted to the hospital for elective surgery. The surgery is billed on one Claim, and ClaimGross is AED 5000. The patient pays a co-pay of AED 400 (ClaimPatientShare is 400).
The hospital charges the payer for the remaining AED 4600. ClaimNet is 4600.</xs:documentation>
      </xs:annotation>
      <xs:restriction base="xs:float"/>
   </xs:simpleType>
   <xs:simpleType name="ClaimDenialCode">
      <xs:annotation>
         <xs:documentation>The denial code if the claim is denied by the payer.
	  The list of denail codes can be found at www.haad.ae/Datadictionary Codes/Codes.</xs:documentation>
      </xs:annotation>
      <xs:restriction base="xs:string"/>
   </xs:simpleType>
   <xs:simpleType name="ClaimPaymentReference">
      <xs:annotation>
         <xs:documentation>The unique identifier for the payment transaction, which depending on the way of payment should contain the following values:
						- The cheque number for payments by a cheque
						- Bank transfer number for payment by a bank transfer
						- Payment voucher number for cash payments</xs:documentation>
      </xs:annotation>
      <xs:restriction base="xs:string">
         <xs:minLength value="1"/>
         <xs:whiteSpace value="collapse"/>
      </xs:restriction>
   </xs:simpleType>
   <xs:simpleType name="ClaimPaymentAmount">
      <xs:annotation>
         <xs:documentation>The amount paid by the payer towards the provider’s Claim.

Example | A payer received a Claim with a net amount of AED 4600 (ClaimNet AED is 4600). 
The payer decides to make deductions of AED 600, and pays the remaining amount. ClaimPaymentAmount is 4000.</xs:documentation>
      </xs:annotation>
      <xs:restriction base="xs:float"/>
   </xs:simpleType>
   <xs:simpleType name="ClaimDateSettlement">
      <xs:annotation>
         <xs:documentation>The date the payer settles the Claim. In general this will be the date that payment is made. If Payment is made in several steps, the latest date should be used. If the value of the Claim agreed by the Payer is 0, then settlement does not entail payment.</xs:documentation>
      </xs:annotation>
      <xs:restriction base="q3:DateTimeForm" xmlns:q3="http://www.haad.ae/DataDictionary/CommonTypes"/>
   </xs:simpleType>
   <xs:simpleType name="ClaimDateSettlementReceived">
      <xs:annotation>
         <xs:documentation>NOTE: Definition of ClaimDateSettlementReceived claim will be reviewed later as per DSP decision 233.
		The date the payee receives payment of the Claim.
		•  If settlement is made in several steps, the latest date of receipt should be used.
		•  If the settlement value is 0, then this is the date of notification of settlement
		•  If the provider has designated an intermediary, e.g., another provider or organization to receive payment, it is the date that designated organization receives payment.

		Restrictions:
		Needs to be between ClaimDateSettled and the present.</xs:documentation>
      </xs:annotation>
      <xs:restriction base="q4:DateTimeForm" xmlns:q4="http://www.haad.ae/DataDictionary/CommonTypes"/>
   </xs:simpleType>
   <xs:simpleType name="EncounterID">
      <xs:annotation>
         <xs:documentation>A unique number assigned by the healthcare provider to identify an Encounter. 

Note | It will help the provider and insurer locate the Encounter. 
This number will also facilitate posting of payment information and identification of the billed Encounter.</xs:documentation>
      </xs:annotation>
      <xs:restriction base="xs:string"/>
   </xs:simpleType>
   <xs:simpleType name="EncounterFacilityID">
      <xs:annotation>
         <xs:documentation>License number of the facility responsible for the Encounter. If reported encounter happened in a not licensed facility, must be equal to “@” followed by the name of the facility.
		Restrictions: Needs to be listed in http://www.shafafiya.org/Dictionary/Licenses/FacilityLicenses.xls or start with “@” .</xs:documentation>
      </xs:annotation>
      <xs:restriction base="xs:string"/>
   </xs:simpleType>
   <xs:simpleType name="EncounterType">
      <xs:annotation>
         <xs:documentation>1 = No Bed + No emergency room 
2 = No Bed + Emergency room
3 = Inpatient Bed + No emergency room
4 = Inpatient Bed + Emergency room
5 = Daycase Bed + No emergency room
6 = Daycase Bed + Emergency room
7 = Nationals Screening
8 = New Visa Screening
9 = Renewal Visa Screening
12 = Home
13 = Assisted Living Facility
15 = Mobile Unit
41 = Ambulance - Land
42 = Ambulance - Air or Water
10 = Telemedicine

Note | There are different ways to classify Encounters as inpatients, daycases, emergencies and outpatients. They vary according to whether the Encounter went past midnight, lasted for more than 24 hours, involved a hospital bed and whether they involved an emergency room. To benchmark with different countries, one needs to know, whether the patient was in the emergency room, and whether the patient occupied a hospital bed.

Inpatient bed | A licensed bed approved by the competent authority which is assigned to a patient who is arriving to a health care facililty for an emergent, urgent or elective/planned Encounter. Beds assigned temporarily for "holding" purposes in a no bed situation may be designated and included in hospital occupancy rate calculation (e.g. emergency room, recovery room). Only beds included in the licensed inpatient bed complement will be used for purposes of hospital occupancy rate calculation. Beds may have an associated accommodation value such as private (i.e. single bed/room) or shared (i.e. multiple beds/room). 

Beds included in the inpatient bed complement:
•  Beds in general wards or units set up and staffed for inpatient services
•  Beds in special care units set up and staffed for inpatient services such as intensive care, coronary care, neonatal intensive care, pediatric intensive care, medical and surgical step-down, burn units
Beds excluded from the inpatient bed complement:
•  Beds/cots for healthy newborns
•  Beds in Day Care units, such as surgical, medical, pediatric day care, interventional radiology
•  Beds in Dialysis units
•  Beds in Labor Suites (e.g. birth day beds, birthing chairs)
•  Beds in Operating Theatre
•  Temporary beds such as stretchers
•  Chairs, Cots or Beds used to accommodate sitters, parents, guardians accompanying patients or sick children and healthy baby accompanying a hospitalized breast feeding mother
•  Beds closed during renovation of patient care areas when approved by the competent authority
Daycase bed | Daycase beds, also known as observation beds, are beds used in Day Care units such as surgical, medical, pediatric day care interventional radiology. They are not included in the inpatient bed complement.

Restrictions:
Only values allowed are:
1 = No Bed + No emergency room 
2 = No Bed + Emergency room
3 = Inpatient Bed + No emergency room
4 = Inpatient Bed + Emergency room
5 = Daycase Bed + No emergency room
6 = Daycase Bed + Emergency room
7 = Nationals Screening
8 = New Visa Screening
9 = Renewal Visa Screening
12 = Home
13 = Assisted Living Facility
15 = Mobile Unit
41 = Ambulance - Land
42 = Ambulance - Air or Water
10 = Telemedicine</xs:documentation>
      </xs:annotation>
      <xs:restriction base="xs:integer">
         <xs:enumeration value="1"/>
         <xs:enumeration value="2"/>
         <xs:enumeration value="3"/>
         <xs:enumeration value="4"/>
         <xs:enumeration value="5"/>
         <xs:enumeration value="6"/>
         <xs:enumeration value="7"/>
         <xs:enumeration value="8"/>
         <xs:enumeration value="9"/>
         <xs:enumeration value="12"/>
         <xs:enumeration value="13"/>
         <xs:enumeration value="15"/>
         <xs:enumeration value="41"/>
         <xs:enumeration value="42"/>
         <xs:enumeration value="10"/>
      </xs:restriction>
   </xs:simpleType>
   <xs:simpleType name="EncounterPatientID">
      <xs:annotation>
         <xs:documentation>The unique number a healthcare provider assigns to a patient. 
	  This is often known as the medical record number.</xs:documentation>
      </xs:annotation>
      <xs:restriction base="xs:string">
         <xs:minLength value="1"/>
         <xs:whiteSpace value="collapse"/>
      </xs:restriction>
   </xs:simpleType>
   <xs:simpleType name="EncounterEligibilityIDPayer">
      <xs:annotation>
         <xs:documentation>The AuthorizationIDPayer provided by the Insurer/TPA in the latest Eligibility transaction (PriorAuthorization with AuthorizationType=Eligibility) and reported in a ClaimsSubmission. This is used for the Provider to demonstrate in the ClaimSubmission that Payer has confirmed patient’s eligibility.</xs:documentation>
      </xs:annotation>
      <xs:restriction base="xs:string"/>
   </xs:simpleType>
   <xs:simpleType name="EncounterStart">
      <xs:annotation>
         <xs:documentation>EncounterStart is the date and time at which the patient comes under the care of a responsible clinician.
•  For Elective patients this will typically be the date and time of the visit registration/admission on arrival of the patient at the healthcare facility. 
•  For Emergency patients this will typically be the date and time of the registration and admission on arrival of the patient at the healthcare facility.
•  For Transfer patients between facilities (i.e. inter-hospital transfers), this will typically be the date and time of the visit registration and admission on arrival of the patient at the receiving healthcare facility.
•  For Livebirth this will typically be the date and time of the registration and admission of the newborn at the healthcare facility. The Encounter start will also be the date and time of birth.
•  For Stillbirth this will typically be the date and time of the registration of the stillborn at the healthcare facility.  The Encounter start will also be the date and time of stillbirth.
•  For Death on arrival this will typically be the date and time of the visit registration on arrival of the patient at the healthcare facility for pronouncement.

Restrictions:
Needs to be after 1/1/1900 and before the present.</xs:documentation>
      </xs:annotation>
      <xs:restriction base="q6:DateTimeForm" xmlns:q6="http://www.haad.ae/DataDictionary/CommonTypes"/>
   </xs:simpleType>
   <xs:simpleType name="EncounterEnd">
      <xs:annotation>
         <xs:documentation>In general this is the time the patient ceases to be under the direct care of a responsible clinician.
	  • For inpatients and day patients this would be the discharge date and time.
	  • For emergency patients this would be the time that the patient was released from the ER.
	  Note | EncounterEnd is not required for outpatients, even though the field logic applies analogously to other encounter types.</xs:documentation>
      </xs:annotation>
      <xs:restriction base="q7:DateTimeForm" xmlns:q7="http://www.haad.ae/DataDictionary/CommonTypes"/>
   </xs:simpleType>
   <xs:simpleType name="EncounterStartType">
      <xs:annotation>
         <xs:documentation>EncounterStartType is 

1 = Elective, i.e., an Encounter is scheduled in advance
2 = Emergency
3 = Transfer admission from acute care
4 = Live birth
5 = Still birth
6 = Dead On Arrival
7 = Continuing Encounter
8 = Transfer admission from non-acute care

Example 1 | An urgent referral from an outpatient clinic to the cardiology ward, i.e., not scheduled, would be considered as EncounterStartType 2 = Emergency, and EncounterType would be 3 = Inpatient bed + No emergency room  

Example 2 | A patient is referred to a consultant, by her general practitioner, and an appointment is scheduled for two weeks later. This outpatient appointment has EncounterStartType 1 = Elective. 

Example 3 | Provider claims long-term care encounter in two claims: 

Claim 1: EncounterStart 01/01/2011 10:00, EncounterEnd 31/01/2011 23:59, EncounterStartType 3=Transfer admission from acute care, EncounterEndType 6=Not discharged

Claim 2: EncounterStart = 01/02/2011 00:00, EncounterEnd = 13/02/2011 13:00, EncounterStartType = 7-Continuing Encounter, EncounterEndType = 5-Deceased
Restrictions: Only values allowed are 
1 = Elective 
2 = Emergency 
3 = Transfer 
4 = Live birth 
5 = Still birth 
6 = Dead On Arrival 
7 = Continuing Encounter
8 = Transfer admission from non-acute care.</xs:documentation>
      </xs:annotation>
      <xs:restriction base="xs:integer">
         <xs:enumeration value="1"/>
         <xs:enumeration value="2"/>
         <xs:enumeration value="3"/>
         <xs:enumeration value="4"/>
         <xs:enumeration value="5"/>
         <xs:enumeration value="6"/>
         <xs:enumeration value="7"/>
         <xs:enumeration value="8"/>
      </xs:restriction>
   </xs:simpleType>
   <xs:simpleType name="EncounterEndType">
      <xs:annotation>
         <xs:documentation>How the patient was discharged.

1 = Discharged with approval
2 = Discharged against advice 
3 = Discharged absent without leave 
4 = Discharge transfer to acute care 
5 = Deceased 
6 = Not discharged 
7 = Discharge transfer to non-acute care
8 = Tele-Medicine resulting in Emergency Management 
9 = Tele-Medicine resulting in Prescription 
10 = Tele-Medicine resulting in Referral 
11 = Tele-Medicine resulting in Follow Up
12 = Tele-Medicine resulting in Self Care</xs:documentation>
      </xs:annotation>
      <xs:restriction base="xs:integer">
         <xs:enumeration value="1"/>
         <xs:enumeration value="2"/>
         <xs:enumeration value="3"/>
         <xs:enumeration value="4"/>
         <xs:enumeration value="5"/>
         <xs:enumeration value="6"/>
         <xs:enumeration value="7"/>
         <xs:enumeration value="8"/>
         <xs:enumeration value="9"/>
         <xs:enumeration value="10"/>
         <xs:enumeration value="11"/>
         <xs:enumeration value="12"/>
      </xs:restriction>
   </xs:simpleType>
   <xs:simpleType name="EncounterSpeciality">
      <xs:annotation>
         <xs:documentation>The predominant specialty of the primary caregiver for the Encounter.

Note | As there are at present no detailed standardized specialty definitions, providers should use their own, pre-existing naming conventions.

Example | Urology
Example | Cardiology.</xs:documentation>
      </xs:annotation>
      <xs:restriction base="xs:string"/>
   </xs:simpleType>
   <xs:simpleType name="EncounterLocation">
      <xs:annotation>
         <xs:documentation>The name used by the provider to describe the location where the Encounter took place. If the patient visited an outpatient clinic, this would be the name used by the provider for the particular clinic. In some cases, where the patient was in multiple inpatient locations while in the healthcare facility, the discharge location should be used.  If the patient was in multiple clinics on the same day, each visit would typically be a separate Encounter, and the clinic location should be reported for each Encounter.

Example | ENT Clinic
Example | Cardiology Ward 3.</xs:documentation>
      </xs:annotation>
      <xs:restriction base="xs:string"/>
   </xs:simpleType>
   <xs:simpleType name="EncounterTransferSource">
      <xs:annotation>
         <xs:documentation>EncounterTransferSource is the healthcare facility from where a hospital transfer originated (EncounterStartType = 3 Transfer). The originating healthcare facility is described by facility license number. Lists of valid license numbers are available on www.shafafiya.org/dictionary >> Licenses. If the facility is not in the list of licensed providers, enter “@” followed by the name of the facility.</xs:documentation>
      </xs:annotation>
      <xs:restriction base="xs:string"/>
   </xs:simpleType>
   <xs:simpleType name="EncounterTransferDestination">
      <xs:annotation>
         <xs:documentation>EncounterTransferDestination is the healthcare facility to which a hospital transfer is made at the end of an Encounter (EncounterEndType = 4 Transfer). This is facility license number. Lists of valid license numbers are available on www.shafafiya.org/dictionary >> Licenses. If the facility is not in the list of licensed providers, enter “@” followed by the name of the facility.</xs:documentation>
      </xs:annotation>
      <xs:restriction base="xs:string"/>
   </xs:simpleType>
   <xs:simpleType name="ActivityID">
      <xs:annotation>
         <xs:documentation>Unique identifier of activity within a claim/authorization.</xs:documentation>
      </xs:annotation>
      <xs:restriction base="xs:string">
         <xs:maxLength value="30"/>
      </xs:restriction>
   </xs:simpleType>
   <xs:simpleType name="ActivityStart">
      <xs:annotation>
         <xs:documentation>The date and time at which Activity started. For DRG code, it is the date and time of discharge. For PriorRequest/Authorization, this refers to the date on which the Activity is scheduled/prescribed to be started, or dispensed (for type=Authorization). Note | If the date, but not the time is recorded, the time should be assumed to be 00:00. Restrictions: Needs to be after 01/01/2005, and before the present except PriorRequest/Authorisation transactions.</xs:documentation>
      </xs:annotation>
      <xs:restriction base="q8:DateTimeForm" xmlns:q8="http://www.haad.ae/DataDictionary/CommonTypes"/>
   </xs:simpleType>
   <xs:simpleType name="ActivityType">
      <xs:annotation>
         <xs:documentation>ActivityType classifies the type of activity. 3 = CPT; 4 = HCPCS; 5 = Trade Drug; 6 = Dental; 8 = Service Code; 9 = IR-DRG; 10 = Generic Drug.</xs:documentation>
      </xs:annotation>
      <xs:restriction base="xs:integer">
         <xs:enumeration value="1"/>
         <xs:enumeration value="2"/>
         <xs:enumeration value="3"/>
         <xs:enumeration value="4"/>
         <xs:enumeration value="5"/>
         <xs:enumeration value="6"/>
         <xs:enumeration value="8"/>
         <xs:enumeration value="9"/>
         <xs:enumeration value="10"/>
      </xs:restriction>
   </xs:simpleType>
   <xs:simpleType name="ActivityCode">
      <xs:annotation>
         <xs:documentation>ActivityCode is the code, specified by ActivityType, for the Activity performed.</xs:documentation>
      </xs:annotation>
      <xs:restriction base="xs:string">
         <xs:minLength value="1"/>
         <xs:whiteSpace value="collapse"/>
      </xs:restriction>
   </xs:simpleType>
   <xs:simpleType name="ActivityQuantity">
      <xs:annotation>
         <xs:documentation>Identifies the number of units (quantity) for a specific Activity.
	  For PriorAuthorizations this refers to the authorized number of units (quantity).
	  The value may have three numbers before decimal separator, and four numbers after decimal separator. Mask: NNN.DDDD.
	  Example 1 | A patient is admitted to the hospital for en elective surgery and was assigned a hospital bed in a private room.
	  The patient stayed at the hospital for 3 days at the private room. The ActivityQuantity for the private room Activity is 3.</xs:documentation>
      </xs:annotation>
      <xs:restriction base="xs:float"/>
   </xs:simpleType>
   <xs:simpleType name="ActivityNet">
      <xs:annotation>
         <xs:documentation>The net charges billed by the provider to the Payer for this Activity.
	  For PriorRequests this is the estimated amount requested, not the amount billed.
	  Note | Some activities will be charged as a line item in a Claim, such as a prescription. Other activities are not charged in their own right.
	  For instance, in a DRG payment system, individual procedures associated with an Encounter may not be charged, but the overall Encounter is charged 
	  as a DRG-Activity.
	  Example | If ActivityType is 9, ActivityCode is 311, ActivityQuantity is 1, ActivityNet might be AED 8250.00.
	  Example | If ActivityType is 1, ActivityCode is 309.3, ActivityQuantity is 1, ActivityNet might be 0, if this procedures is claimed as a DRG.
	  Note | For non-paying, non-insured patients, where a pro-forma invoice is created, this should be the gross amount that would have been charged.</xs:documentation>
      </xs:annotation>
      <xs:restriction base="xs:float"/>
   </xs:simpleType>
   <xs:simpleType name="ActivityList">
      <xs:annotation>
         <xs:documentation>ActivityList describes the list price before any adjustments of discounts.</xs:documentation>
      </xs:annotation>
      <xs:restriction base="xs:float"/>
   </xs:simpleType>
   <xs:simpleType name="ActivityOrderingClinician">
      <xs:annotation>
         <xs:documentation>License number of the clinician who ordered the service or referred the patient for the service.</xs:documentation>
      </xs:annotation>
      <xs:restriction base="xs:string"/>
   </xs:simpleType>
   <xs:simpleType name="ActivityClinician">
      <xs:annotation>
         <xs:documentation>License number of the clinician responsible for the activity. In general the Activity Clinician is the person providing the treatment or care for the patient. Exceptions:
      • for the following services the Activity Clinician is the ordering physician: all types of rehabilitation therapy (includes physiotherapy, speech therapy, occupational therapy, respiratory therapy and dietician services), shockwave therapy, haemodialysis, light therapy and photo-therapy, electro-cautery, labs, x-rays, prescriptions, other tests, such as EEG, Sleep lab and Nerve conduction Studies, Audiometry (effective until ActivityOrderingClinician element is implemented)
      • the Activity Clinician is the attending consultant physician at the time of discharge of the patient from the hospital if the Activity is an inpatient Service Code or DRG</xs:documentation>
      </xs:annotation>
      <xs:restriction base="xs:string"/>
   </xs:simpleType>
   <xs:simpleType name="ActivityPriorAuthorizationID">
      <xs:annotation>
         <xs:documentation>The Prior Authorization ID.</xs:documentation>
      </xs:annotation>
      <xs:restriction base="xs:string"/>
   </xs:simpleType>
   <xs:simpleType name="ActivityGross">
      <xs:annotation>
         <xs:documentation>Is the total AED amount of the charges included on the Activity.
RemittanceActivityGross includes any patient financial responsibility for the Activity, such as co-pays and deductibles, as well as charges made to other insurers for the Encounter(s) covered by the Activity.</xs:documentation>
      </xs:annotation>
      <xs:restriction base="xs:float"/>
   </xs:simpleType>
   <xs:simpleType name="ActivityPatientShare">
      <xs:annotation>
         <xs:documentation>Any fee that Payer is expecting the Provider to collect from the patient.</xs:documentation>
      </xs:annotation>
      <xs:restriction base="xs:float"/>
   </xs:simpleType>
   <xs:simpleType name="ActivityPaymentAmount">
      <xs:annotation>
         <xs:documentation>For RemittanceAdvice: the amount paid by the payer towards the provider’s Claim.
For PriorAuthorization: the amount of guaranteed payment for the activity. 
Example | A payer received a Claim with ActivityNet amount of AED 460. The payer decides to make deductions of AED 60, and pays the remaining amount. ActivityPaymentAmount is 400.</xs:documentation>
      </xs:annotation>
      <xs:restriction base="xs:float"/>
   </xs:simpleType>
   <xs:simpleType name="ActivityDenialCode">
      <xs:annotation>
         <xs:documentation>The denial code if the claim is denied by the payer. The list of denail codes can be found at www.shafafiya.org/dictionary >> Codes >> Other Codes.</xs:documentation>
      </xs:annotation>
      <xs:restriction base="xs:string"/>
   </xs:simpleType>
   <xs:simpleType name="ObservationType">
      <xs:annotation>
         <xs:documentation/>
      </xs:annotation>
      <xs:restriction base="xs:string">
         <xs:enumeration value="CPT"/>
         <xs:enumeration value="HL7v3 Native"/>
         <xs:enumeration value="LOINC"/>
         <xs:enumeration value="SNOMED CT"/>
         <xs:enumeration value="Text"/>
         <xs:enumeration value="File"/>
         <xs:enumeration value="Flags"/>
         <xs:enumeration value="Universal Dental"/>
         <xs:enumeration value="Episode"/>
      </xs:restriction>
   </xs:simpleType>
   <xs:simpleType name="ObservationCode">
      <xs:annotation>
         <xs:documentation>The code describing the Observation value.</xs:documentation>
      </xs:annotation>
      <xs:restriction base="xs:string">
         <xs:minLength value="1"/>
         <xs:whiteSpace value="collapse"/>
      </xs:restriction>
   </xs:simpleType>
   <xs:simpleType name="ObservationValue">
      <xs:annotation>
         <xs:documentation>The observed value of the Activity.

Restriction:
Must be expressed in SI Units.</xs:documentation>
      </xs:annotation>
      <xs:restriction base="xs:string"/>
   </xs:simpleType>
   <xs:simpleType name="ObservationValueType">
      <xs:annotation>
         <xs:documentation>Unit of measure for the ObservationValue.</xs:documentation>
      </xs:annotation>
      <xs:restriction base="xs:string"/>
   </xs:simpleType>
   <xs:simpleType name="DiagnosisType">
      <xs:annotation>
         <xs:documentation>The type of diagnosis being recorded.

Principal: Identifies the principal diagnosis code (full ICD-9-CM) for the condition established after examination. It will identify the nature of a disease or illness.
• Inpatients | Condition established, after study, to be chiefly responsible for occasioning the admission of the patient to the hospital for care. 
• Ambulatory patients | The condition or problem that explains the clinician’s assessment of the presenting symptoms/problems and corresponds to the tests or services provided. This assessment may be a suspected diagnosis or a rule-out diagnosis and is based on the patient’s presenting history and physical and the physician’s review of symptoms. This may also be a symptom where the underlying cause has yet to be determined 

Secondary: 
• Inpatients | All conditions that co-exist at the time of admission, or develop subsequently, which affect the treatment received and/or the length of stay. Diagnoses that refer to an earlier episode that have no bearing on the current hospital stay are to be excluded. Conditions should be coded that affect patient care in terms of requiring: Clinical evaluation, therapeutic treatment, diagnostic procedures, extended length of hospital stay, increased nursing care and/or monitoring. 
• Ambulatory patients | All co-existing conditions, including chronic conditions that exist at the time of the Encounter or visit and require or affect patient management. 
• External causes of injury, poisoning or adverse affect are coded as supplementary codes to the diagnosis codes of the actual condition such as “Motor Vehicle Accident” that caused a fracture of the tibia. 
Note | For quality purposes, it is important to be able to track Hospital-acquired infections. The corresponding E-Code is 849.7 

Admitting:
The diagnosis that the physician identifies at the time of admission.
Note | This diagnosis might differ from EncounterDiagnosisPrincipal.</xs:documentation>
      </xs:annotation>
      <xs:restriction base="xs:string">
         <xs:enumeration value="Principal"/>
         <xs:enumeration value="Secondary"/>
         <xs:enumeration value="Admitting"/>
         <xs:enumeration value="ReasonForVisit"/>
         <xs:minLength value="1"/>
         <xs:whiteSpace value="collapse"/>
      </xs:restriction>
   </xs:simpleType>
   <xs:simpleType name="DiagnosisCode">
      <xs:annotation>
         <xs:documentation>The value for the diagnosis code as per coding manual. The coding manual can be downloaded from www.shafafiya.org/dictionary.</xs:documentation>
      </xs:annotation>
      <xs:restriction base="xs:string">
         <xs:minLength value="1"/>
         <xs:whiteSpace value="collapse"/>
      </xs:restriction>
   </xs:simpleType>
   <xs:simpleType name="DxInfoType">
      <xs:annotation>
         <xs:documentation>The type of additional information for the diagnosis.
      POA : Present On Admission (POA) indicator.
      Year of Onset.</xs:documentation>
      </xs:annotation>
      <xs:restriction base="xs:string">
         <xs:enumeration value="POA"/>
         <xs:enumeration value="Year of Onset"/>
         <xs:enumeration value="Birth Weight"/>
         <xs:enumeration value="ICD10-GM"/>
         <xs:enumeration value="ICD10-CM"/>
         <xs:enumeration value="KCD"/>
         <xs:minLength value="1"/>
         <xs:whiteSpace value="collapse"/>
      </xs:restriction>
   </xs:simpleType>
   <xs:simpleType name="DxInfoCode">
      <xs:annotation>
         <xs:documentation>The code value related to the DxInfoType.</xs:documentation>
      </xs:annotation>
      <xs:restriction base="xs:string">
         <xs:minLength value="1"/>
         <xs:whiteSpace value="collapse"/>
      </xs:restriction>
   </xs:simpleType>
   <xs:simpleType name="ResubmissionType">
      <xs:annotation>
         <xs:documentation>The type of resubmission of a claim or prior request.
Validation rule: value ‘legacy’ is not allowed for PriorRequest</xs:documentation>
      </xs:annotation>
      <xs:restriction base="xs:string">
         <xs:enumeration value="correction"/>
         <xs:enumeration value="internal complaint"/>
         <xs:enumeration value="legacy"/>
      </xs:restriction>
   </xs:simpleType>
   <xs:simpleType name="ResubmissionComment">
      <xs:annotation>
         <xs:documentation/>
      </xs:annotation>
      <xs:restriction base="xs:string">
         <xs:minLength value="1"/>
         <xs:maxLength value="2000"/>
         <xs:whiteSpace value="collapse"/>
      </xs:restriction>
   </xs:simpleType>
   <xs:simpleType name="ResubmissionAttachments">
      <xs:annotation>
         <xs:documentation/>
      </xs:annotation>
      <xs:restriction base="xs:base64Binary"/>
   </xs:simpleType>
   <xs:simpleType name="AuthorizationType">
      <xs:annotation>
         <xs:documentation>Specifies Type using Values: Eligibility, Authorization, Cancellation, Extension, Status Inquiry, Prescription. 
			Based on this Type certain optional elements in the transaction may become mandatory.</xs:documentation>
      </xs:annotation>
      <xs:restriction base="xs:string">
         <xs:enumeration value="Eligibility"/>
         <xs:enumeration value="Authorization"/>
         <xs:enumeration value="Cancellation"/>
         <xs:enumeration value="Extension"/>
         <xs:enumeration value="Status Inquiry"/>
         <xs:enumeration value="Prescription"/>
         <xs:minLength value="1"/>
         <xs:whiteSpace value="collapse"/>
      </xs:restriction>
   </xs:simpleType>
   <xs:simpleType name="AuthorizationResult">
      <xs:annotation>
         <xs:documentation>The answer of the inquiry: Yes or No.</xs:documentation>
      </xs:annotation>
      <xs:restriction base="xs:string"/>
   </xs:simpleType>
   <xs:simpleType name="AuthorizationID">
      <xs:annotation>
         <xs:documentation>The unique identifier assigned by the health provider to identify the Authorization; must be globally unique and start with EncounterFacilityID followed by a unique identifier assigned by the facility information system. Example: PF1223-00145677.</xs:documentation>
      </xs:annotation>
      <xs:restriction base="xs:string">
         <xs:minLength value="1"/>
         <xs:whiteSpace value="collapse"/>
      </xs:restriction>
   </xs:simpleType>
   <xs:simpleType name="AuthorizationIDPayer">
      <xs:annotation>
         <xs:documentation>The unique number assigned by an insurer to identify the Authorization.</xs:documentation>
      </xs:annotation>
      <xs:restriction base="xs:string"/>
   </xs:simpleType>
   <xs:simpleType name="AuthorizationMemberID">
      <xs:annotation>
         <xs:documentation>The patient’s insurance member number as shown on insurance membership card. For self-pay, must be equal to EncounterFacilityID#EncounterPatientID.</xs:documentation>
      </xs:annotation>
      <xs:restriction base="xs:string">
         <xs:minLength value="1"/>
         <xs:whiteSpace value="collapse"/>
      </xs:restriction>
   </xs:simpleType>
   <xs:simpleType name="AuthorizationPayerID">
      <xs:annotation>
         <xs:documentation>If the patient is claiming insurance cover, this is the Insurer's license number. Lists of valid license numbers are available on www.shafafiya.org/dictionary >> Licenses. If the patient is claiming insurance from an Insurer not included in the list of valid licenses, this should be “@” followed by the name of the Insurer If the patient is paying directly for services provided, this should be “SelfPay”. If the patient neither claims insurance nor pays directly for services provided, this should be “ProFormaPayer”. Example | H018 Example | @Cigna Medical Example | SelfPay Example | ProFormaPayer.</xs:documentation>
      </xs:annotation>
      <xs:restriction base="xs:string">
         <xs:minLength value="1"/>
         <xs:whiteSpace value="collapse"/>
      </xs:restriction>
   </xs:simpleType>
   <xs:simpleType name="AuthorizationEmiratesIDNumber">
      <xs:annotation>
         <xs:documentation>The unique number the government assigns to a citizen. When an EmiratesIDNumber is not available:
      000-0000-0000000-0 National without card
      111-1111-1111111-1 Expatriate resident without a card
      222-2222-2222222-2 Non national, non-expat resident without a card
      999-9999-9999999-9 Unknown status, without a card.</xs:documentation>
      </xs:annotation>
      <xs:restriction base="xs:string">
         <xs:minLength value="1"/>
         <xs:whiteSpace value="collapse"/>
      </xs:restriction>
   </xs:simpleType>
   <xs:simpleType name="AuthorizationDenialCode">
      <xs:annotation>
         <xs:documentation>The denial code if the authorization is denied by the payer. The list of denail codes can be found at www.shafafiya.org/dictionary >> Codes >> Other Codes.</xs:documentation>
      </xs:annotation>
      <xs:restriction base="xs:string"/>
   </xs:simpleType>
   <xs:simpleType name="AuthorizationDateOrdered">
      <xs:annotation>
         <xs:documentation>The date on which the prescription/order is ordered/prescribed.
			This is required to check, e.g., validity of a prescription/order, or onset of condition to exclude pre-existing conditions as per policy coverage.</xs:documentation>
      </xs:annotation>
      <xs:restriction base="q1:DateForm" xmlns:q1="http://www.haad.ae/DataDictionary/CommonTypes"/>
   </xs:simpleType>
   <xs:simpleType name="AuthorizationStart">
      <xs:annotation>
         <xs:documentation>The date and time at which Activity started.</xs:documentation>
      </xs:annotation>
      <xs:restriction base="q1:DateTimeForm" xmlns:q1="http://www.haad.ae/DataDictionary/CommonTypes"/>
   </xs:simpleType>
   <xs:simpleType name="AuthorizationEnd">
      <xs:annotation>
         <xs:documentation>The date and time at which Activity ended.</xs:documentation>
      </xs:annotation>
      <xs:restriction base="q1:DateTimeForm" xmlns:q1="http://www.haad.ae/DataDictionary/CommonTypes"/>
   </xs:simpleType>
   <xs:simpleType name="AuthorizationLimit">
      <xs:annotation>
         <xs:documentation>The total amount available at the time of prior authorization. The communication of the authorization limit is advisory in nature and no provisions or limits are guaranteed for any period of time.</xs:documentation>
      </xs:annotation>
      <xs:restriction base="xs:float"/>
   </xs:simpleType>
   <xs:simpleType name="AuthorizationComments">
      <xs:annotation>
         <xs:documentation>The comments given to add more details on the Authorization.</xs:documentation>
      </xs:annotation>
      <xs:restriction base="xs:string"/>
   </xs:simpleType>
   <!--Started New Element Changes-->
   <xs:simpleType name="PersonUnifiedNumber">
      <xs:annotation>
         <xs:documentation>Unique number issued by MOI to identity resident of UAE uniquely</xs:documentation>
      </xs:annotation>
      <xs:restriction base="xs:integer"/>
   </xs:simpleType>
   <xs:simpleType name="PersonFirstNameEn">
      <xs:annotation>
         <xs:documentation>Person First name English for whome Insurance policy is issued</xs:documentation>
      </xs:annotation>
      <xs:restriction base="xs:string"/>
   </xs:simpleType>
   <xs:simpleType name="PersonMiddleNameEn">
      <xs:annotation>
         <xs:documentation>Person Second name English for whome Insurance policy is issued</xs:documentation>
      </xs:annotation>
      <xs:restriction base="xs:string"/>
   </xs:simpleType>
   <xs:simpleType name="PersonLastNameEn">
      <xs:annotation>
         <xs:documentation>Person Family name English for whome Insurance policy is issued</xs:documentation>
      </xs:annotation>
      <xs:restriction base="xs:string"/>
   </xs:simpleType>
   <xs:simpleType name="PersonFirstNameAr">
      <xs:annotation>
         <xs:documentation>Person First name Arabic for whome Insurance policy is issued</xs:documentation>
      </xs:annotation>
      <xs:restriction base="xs:string"/>
   </xs:simpleType>
   <xs:simpleType name="PersonMiddleNameAr">
      <xs:annotation>
         <xs:documentation>Person Second name Arabic for whome Insurance policy is issued</xs:documentation>
      </xs:annotation>
      <xs:restriction base="xs:string"/>
   </xs:simpleType>
   <xs:simpleType name="PersonLastNameAr">
      <xs:annotation>
         <xs:documentation>Person Family name Arabic for whome Insurance policy is issued</xs:documentation>
      </xs:annotation>
      <xs:restriction base="xs:string"/>
   </xs:simpleType>
   <xs:simpleType name="PersonNationalityCode">
      <xs:annotation>
         <xs:documentation>Nationality Code of Person to whom Insurance policy is issued</xs:documentation>
      </xs:annotation>
      <xs:restriction base="xs:integer"/>
   </xs:simpleType>
   <xs:simpleType name="PersonCityCode">
      <xs:annotation>
         <xs:documentation>Residenace City Code of Person to whom Insurance policy is issued</xs:documentation>
      </xs:annotation>
      <xs:restriction base="xs:integer"/>
   </xs:simpleType>
   <!--Ended New Element Changes-->
   <!--Started New Element Changes for VAT-->
   <xs:simpleType name="ClaimVAT">
      <xs:annotation>
         <xs:documentation>The total Value Added Tax amount appropriated for the claim</xs:documentation>
      </xs:annotation>
      <xs:restriction base="xs:float"/>
   </xs:simpleType>
   <xs:simpleType name="ActivityVAT">
      <xs:annotation>
         <xs:documentation>The Value Added Tax amount appropriated for the activity</xs:documentation>
      </xs:annotation>
      <xs:restriction base="xs:float"/>
   </xs:simpleType>
   <xs:simpleType name="ActivityVATPercent">
      <xs:annotation>
         <xs:documentation>the Value Added Tax rate</xs:documentation>
      </xs:annotation>
      <xs:restriction base="xs:float"/>
   </xs:simpleType>
   <xs:simpleType name="ContractCollectedPremium">
      <xs:annotation>
         <xs:documentation>The AED amount received by the Payer as a premium for the term of the Member’s insurance contract</xs:documentation>
      </xs:annotation>
      <xs:restriction base="xs:float"/>
   </xs:simpleType>
   <xs:simpleType name="ContractVAT">
      <xs:annotation>
         <xs:documentation>The total Value Added Tax amount appropriated for the collected premium</xs:documentation>
      </xs:annotation>
      <xs:restriction base="xs:float"/>
   </xs:simpleType>
   <xs:simpleType name="ContractVATPercent">
      <xs:annotation>
         <xs:documentation>The Value Added Tax rate</xs:documentation>
      </xs:annotation>
      <xs:restriction base="xs:float"/>
   </xs:simpleType>
   <!--Ended New Element Changes for VAT-->
   <!--Started New Element Changes-->
   <xs:simpleType name="PersonCountryOfResidence">
      <xs:annotation>
         <xs:documentation>Name of the Person’s country of usual residence. Includes value from the column ‘Country’ at
       www.haad.ae/shafafiya/dictionary >> Other Codes >> Nationality
       Example: United Arab Emirates</xs:documentation>
      </xs:annotation>
      <xs:restriction base="xs:string"/>
   </xs:simpleType>
   <xs:simpleType name="PersonEmirateOfResidence">
      <xs:annotation>
         <xs:documentation>Code of the Person’s Emirate of usual residence, if the Person is a resident of the UAE. Includes value from the column ‘Code’ at
       www.haad.ae/shafafiya/dictionary >> Other Codes >> Emirate
       Example: If the person is a resident of Abu Dhabi, Person.EmirateOfresidence=1</xs:documentation>
      </xs:annotation>
      <xs:restriction base="xs:string"/>
   </xs:simpleType>
   <!--Ended New Element Changes-->
   <xs:simpleType name="ActivityDateOrdered">
      <xs:annotation>
         <xs:documentation>The date and time at which Activity DateOrdered.</xs:documentation>
      </xs:annotation>
      <xs:restriction base="q8:DateTimeForm" xmlns:q8="http://www.haad.ae/DataDictionary/CommonTypes"/>
   </xs:simpleType>
   <!--Started New Element Changes on Oct 2022-->
   <xs:simpleType name="PersonSponsorNumber">
      <xs:annotation>
         <xs:documentation>An employer or Individual Number that offers a group health plan to its employees or members</xs:documentation>
      </xs:annotation>
      <xs:restriction base="xs:string"/>
   </xs:simpleType>
   <xs:simpleType name="PersonSponsorNameEn">
      <xs:annotation>
         <xs:documentation>An employer or Individual English name that offers a group health plan to its employees or members</xs:documentation>
      </xs:annotation>
      <xs:restriction base="xs:string"/>
   </xs:simpleType>
   <xs:simpleType name="PersonSponsorNameAr">
      <xs:annotation>
         <xs:documentation>An employer or Individual Arabic name that offers a group health plan to its employees or members</xs:documentation>
      </xs:annotation>
      <xs:restriction base="xs:string"/>
   </xs:simpleType>
   <xs:simpleType name="ContractStatus">
      <xs:annotation>
         <xs:documentation>
      •	New – The insurance company issuing the contract first time
	  •	Restarted – The same contract restarted after some time
	  •	Renewed – The insurance company renewed the contract 
	  •	Corrected – Member correction for the current contract
	  •	Corrected Date - Contract date correction for the current contract
	  •	Updated EmiratesIDNumber – Update the original emiratesIDNumber for the current contract
	  •	Cancelled – Current contract cancellation
	  •	Gap Enrollment – 
	  •	Recon – Historical Records or Missing years
	  •	Newborn – Newborn under Mother Coverage – 30 days
	  •	WarZone - 6 Months Emergency Insurance provided to Members of Warzone Countries
	  •	Visitors – Visitors Emergency Insurance</xs:documentation>
      </xs:annotation>
      <xs:restriction base="xs:string">
         <xs:enumeration value="New"/>
         <xs:enumeration value="Restarted"/>
         <xs:enumeration value="Renewed"/>
         <xs:enumeration value="Corrected"/>
         <xs:enumeration value="Corrected Date"/>
         <xs:enumeration value="Updated EmiratesIDNumber"/>
         <xs:enumeration value="Cancelled"/>
         <xs:enumeration value="Gap Enrollment"/>
         <xs:enumeration value="Recon"/>
         <xs:enumeration value="Newborn"/>
         <xs:enumeration value="WarZone"/>
         <xs:enumeration value="Visitor"/>
		 <xs:enumeration value="Newborn-correction"/>
		 <xs:enumeration value="Newborn-cancellation"/>
      </xs:restriction>
   </xs:simpleType>
   <!--Ended New Element Changes on Oct 2022-->
   <xs:simpleType name="MemberRelationToEmiratesIDNumber">
      <xs:annotation>
         <xs:documentation>The information about the Emirates ID Number of the principal member of the family.</xs:documentation>
      </xs:annotation>
      <xs:restriction base="xs:string"/>
   </xs:simpleType>
   <xs:simpleType name="MemberRelationToUnifiedNumber">
      <xs:annotation>
         <xs:documentation>The information about the Unified Number of the principal member of the family.</xs:documentation>
      </xs:annotation>
      <xs:restriction base="xs:string"/>
   </xs:simpleType>
   <xs:simpleType name="PersonSpecialNationality">
      <xs:annotation>
         <xs:documentation>Special Nationality Identification</xs:documentation>
      </xs:annotation>
      <xs:restriction base="xs:string"/>
   </xs:simpleType>
   <xs:simpleType name="PersonPrivileges">
      <xs:annotation>
         <xs:documentation>Person Privileges</xs:documentation>
      </xs:annotation>
      <xs:restriction base="xs:string"/>
   </xs:simpleType>
   <xs:simpleType name="PersonCOCReferenceNumber">
      <xs:annotation>
         <xs:documentation>Member COC Reference Number</xs:documentation>
      </xs:annotation>
      <xs:restriction base="xs:string"/>
   </xs:simpleType>
   <xs:simpleType name="PersonBirthCertificateNumber">
      <xs:annotation>
         <xs:documentation>Baby's Birth Certificate Number</xs:documentation>
      </xs:annotation>
      <xs:restriction base="xs:string"/>
   </xs:simpleType>
</xs:schema>