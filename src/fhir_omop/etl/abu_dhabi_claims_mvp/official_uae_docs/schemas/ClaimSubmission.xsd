<xs:schema elementFormDefault="qualified" version="2.0" id="ClaimSubmission" xmlns:tns="http://www.haad.ae/DataDictionary/CommonTypes" xmlns:xs="http://www.w3.org/2001/XMLSchema">
  <xs:import schemaLocation="CommonTypes.xsd" namespace="http://www.haad.ae/DataDictionary/CommonTypes"/>
  <xs:element name="Claim.Submission">
    <xs:complexType>
      <xs:sequence>
        <xs:element name="Header">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="SenderID" type="tns:HeaderSenderID"/>
              <xs:element name="ReceiverID" type="tns:HeaderReceiverID"/>
              <xs:element name="TransactionDate" type="tns:HeaderTransactionDate"/>
              <xs:element name="RecordCount" type="tns:HeaderRecordCount"/>
              <xs:element name="DispositionFlag" type="tns:HeaderDispositionFlag"/>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="Claim" maxOccurs="unbounded">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="ID" type="tns:ClaimID"/>
              <xs:element name="IDPayer" type="tns:ClaimIDPayer" minOccurs="0"/>
              <xs:element name="MemberID" type="tns:ClaimMemberID" minOccurs="0"/>
              <xs:element name="PayerID" type="tns:ClaimPayerID"/>
              <xs:element name="ProviderID" type="tns:ClaimProviderID"/>
              <xs:element name="EmiratesIDNumber" type="tns:ClaimEmiratesIDNumber"/>
              <xs:element name="Gross" type="tns:ClaimGross"/>
              <xs:element name="PatientShare" type="tns:ClaimPatientShare"/>
              <xs:element name="Net" type="tns:ClaimNet"/>
              <xs:element name="VAT" type="tns:ClaimVAT" minOccurs="0"/>
              <xs:element name="Encounter" maxOccurs="unbounded">
                <xs:complexType>
                  <xs:sequence>
                    <xs:element name="FacilityID" type="tns:EncounterFacilityID"/>
                    <xs:element name="Type" type="tns:EncounterType"/>
                    <xs:element name="PatientID" type="tns:EncounterPatientID"/>
                    <xs:element name="EligibilityIDPayer" type="tns:EncounterEligibilityIDPayer" minOccurs="0"/>
                    <xs:element name="Start" type="tns:EncounterStart"/>
                    <xs:element name="End" type="tns:EncounterEnd" minOccurs="0"/>
                    <xs:element name="StartType" type="tns:EncounterStartType" minOccurs="0"/>
                    <xs:element name="EndType" type="tns:EncounterEndType" minOccurs="0"/>
                    <xs:element name="TransferSource" type="tns:EncounterTransferSource" minOccurs="0"/>
                    <xs:element name="TransferDestination" type="tns:EncounterTransferDestination" minOccurs="0"/>
                  </xs:sequence>
                </xs:complexType>
              </xs:element>
              <xs:element name="Diagnosis" maxOccurs="unbounded">
                <xs:complexType>
                  <xs:sequence>
                    <xs:element name="Type" type="tns:DiagnosisType"/>
                    <xs:element name="Code" type="tns:DiagnosisCode"/>
                    <xs:element name="DxInfo" minOccurs="0" maxOccurs="unbounded">
                      <xs:complexType>
                        <xs:sequence>
                          <xs:element name="Type" type="tns:DxInfoType"/>
                          <xs:element name="Code" type="tns:DxInfoCode"/>
                        </xs:sequence>
                      </xs:complexType>
                    </xs:element>
                  </xs:sequence>
                </xs:complexType>
              </xs:element>
              <xs:element name="Activity" maxOccurs="unbounded">
                <xs:complexType>
                  <xs:sequence>
                    <xs:element name="ID" type="tns:ActivityID" minOccurs="0"/>
                    <xs:element name="Start" type="tns:ActivityStart"/>
                    <xs:element name="Type" type="tns:ActivityType"/>
                    <xs:element name="Code" type="tns:ActivityCode"/>
                    <xs:element name="Quantity" type="tns:ActivityQuantity"/>
                    <xs:element name="Net" type="tns:ActivityNet"/>
                    <xs:element name="OrderingClinician" type="tns:ActivityOrderingClinician" minOccurs="0"/>
                    <xs:element name="Clinician" type="tns:ActivityClinician" minOccurs="0"/>
                    <xs:element name="PriorAuthorizationID" type="tns:ActivityPriorAuthorizationID" minOccurs="0"/>
                    <!-- Started New Element Changes for VAT-->
                    <xs:element name="VAT" type="tns:ActivityVAT" minOccurs="0"/>
                    <xs:element name="VATPercent" type="tns:ActivityVATPercent" minOccurs="0"/>
                    <!-- Ended New Element Changes for VAT-->
                    <xs:element name="DateOrdered" type="tns:ActivityDateOrdered" minOccurs="0"/>
                    <xs:element name="Observation" minOccurs="0" maxOccurs="unbounded">
                      <xs:complexType>
                        <xs:sequence>
                          <xs:element name="Type" type="tns:ObservationType"/>
                          <xs:element name="Code" type="tns:ObservationCode"/>
                          <xs:element name="Value" type="tns:ObservationValue" minOccurs="0"/>
                          <xs:element name="ValueType" type="tns:ObservationValueType" minOccurs="0"/>
                        </xs:sequence>
                      </xs:complexType>
                    </xs:element>
                  </xs:sequence>
                </xs:complexType>
              </xs:element>
              <xs:element name="Resubmission" minOccurs="0">
                <xs:complexType>
                  <xs:sequence>
                    <xs:element name="Type" type="tns:ResubmissionType"/>
                    <xs:element name="Comment" type="tns:ResubmissionComment"/>
                    <xs:element name="Attachment" type="tns:ResubmissionAttachments" minOccurs="0"/>
                  </xs:sequence>
                </xs:complexType>
              </xs:element>
              <xs:element name="Contract" minOccurs="0">
                <xs:complexType>
                  <xs:sequence>
                    <xs:element name="PackageName" type="tns:ContractPackageName" minOccurs="0"/>
                  </xs:sequence>
                </xs:complexType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>        
      </xs:sequence>
    </xs:complexType>
  </xs:element>
</xs:schema>