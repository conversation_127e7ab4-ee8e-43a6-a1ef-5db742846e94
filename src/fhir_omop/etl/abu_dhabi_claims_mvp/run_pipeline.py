#!/usr/bin/env python3
"""
Abu Dhabi Claims to OMOP ETL Pipeline

Main execution script that orchestrates the complete ETL process.
Reproduces notebook results in automated fashion.

Author: FHIR-OMOP Development Team
Created: December 2024
License: MIT
"""

import os
import sys
import argparse
import logging
import time
from pathlib import Path
from typing import Dict, Any

import yaml
from dotenv import load_dotenv

# Add current directory to path for local imports
sys.path.append(str(Path(__file__).parent))

from extract_claims import extract_claims_data, filter_cpt_procedures, validate_extracted_data
from transform_omop import transform_all_tables
from load_database import load_omop_tables, create_connection_string, validate_loaded_data

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def parse_args():
    """Parse command line arguments.
    
    Returns
    -------
    argparse.Namespace
        Parsed command line arguments
    """
    parser = argparse.ArgumentParser(
        description="Abu Dhabi Claims to OMOP ETL Pipeline"
    )
    parser.add_argument(
        "--config",
        default=os.getenv("ETL_CONFIG", "config.yaml"),
        help="Path to configuration file"
    )
    parser.add_argument(
        "--input-file",
        default=os.getenv("INPUT_FILE"),
        help="Override input CSV file path"
    )
    parser.add_argument(
        "--dry-run",
        action="store_true",
        help="Validate configuration and connections without processing data"
    )
    parser.add_argument(
        "--skip-load",
        action="store_true", 
        help="Skip database loading (extract and transform only)"
    )
    parser.add_argument(
        "--validate-only",
        action="store_true",
        help="Only validate data without transformation or loading"
    )
    parser.add_argument(
        "--environment",
        default=os.getenv("ETL_ENVIRONMENT", "development"),
        choices=["development", "testing", "production"],
        help="Environment configuration to use"
    )
    return parser.parse_args()


def load_configuration(config_path: str, environment: str = "development") -> Dict[str, Any]:
    """Load and merge configuration from YAML file.
    
    Parameters
    ----------
    config_path : str
        Path to the configuration YAML file
    environment : str, optional
        Environment name for configuration overrides, by default "development"
        
    Returns
    -------
    Dict[str, Any]
        Merged configuration dictionary
        
    Raises
    ------
    FileNotFoundError
        If configuration file is not found
    yaml.YAMLError
        If configuration file has invalid YAML syntax
    """
    logger.info(f"Loading configuration from: {config_path}")
    
    if not Path(config_path).exists():
        raise FileNotFoundError(f"Configuration file not found: {config_path}")
    
    try:
        with open(config_path, 'r') as f:
            config = yaml.safe_load(f)
        
        # Apply environment-specific overrides
        if 'environments' in config and environment in config['environments']:
            env_config = config['environments'][environment]
            config = merge_configs(config, env_config)
            logger.info(f"Applied {environment} environment configuration")
        
        return config
        
    except yaml.YAMLError as e:
        raise yaml.YAMLError(f"Error parsing configuration file: {e}")


def merge_configs(base_config: Dict[str, Any], override_config: Dict[str, Any]) -> Dict[str, Any]:
    """Recursively merge configuration dictionaries.
    
    Parameters
    ----------
    base_config : Dict[str, Any]
        Base configuration dictionary
    override_config : Dict[str, Any]
        Override configuration dictionary
        
    Returns
    -------
    Dict[str, Any]
        Merged configuration dictionary
    """
    merged = base_config.copy()
    
    for key, value in override_config.items():
        if key in merged and isinstance(merged[key], dict) and isinstance(value, dict):
            merged[key] = merge_configs(merged[key], value)
        else:
            merged[key] = value
    
    return merged


def validate_configuration(config: Dict[str, Any]) -> bool:
    """Validate configuration has required sections and values.
    
    Parameters
    ----------
    config : Dict[str, Any]
        Configuration dictionary to validate
        
    Returns
    -------
    bool
        True if configuration is valid
    """
    logger.info("Validating configuration")
    
    required_sections = ['database', 'data', 'field_mappings']
    missing_sections = []
    
    for section in required_sections:
        if section not in config:
            missing_sections.append(section)
    
    if missing_sections:
        logger.error(f"Missing required configuration sections: {missing_sections}")
        return False
    
    # Validate database configuration
    db_config = config['database']
    required_db_fields = ['host', 'port', 'database', 'user']
    missing_db_fields = [field for field in required_db_fields if field not in db_config]
    
    if missing_db_fields:
        logger.error(f"Missing required database fields: {missing_db_fields}")
        return False
    
    logger.info("Configuration validation passed")
    return True


def run_etl_pipeline(config: Dict[str, Any], input_file: str = None, 
                    skip_load: bool = False, validate_only: bool = False) -> Dict[str, Any]:
    """Execute the complete ETL pipeline.
    
    Reproduces notebook results in automated fashion.
    
    Parameters
    ----------
    config : Dict[str, Any]
        Configuration dictionary
    input_file : str, optional
        Override input file path
    skip_load : bool, optional
        Skip database loading, by default False
    validate_only : bool, optional
        Only validate data without transformation, by default False
        
    Returns
    -------
    Dict[str, Any]
        Pipeline execution results
    """
    logger.info("🚀 Starting Abu Dhabi Claims to OMOP ETL Pipeline")
    start_time = time.time()
    
    results = {
        'start_time': start_time,
        'steps_completed': [],
        'validation_results': {},
        'load_results': {},
        'total_records': 0,
        'success': False
    }
    
    try:
        # Step 1: Extract claims data
        logger.info("📥 Step 1: Extracting claims data")
        csv_path = input_file or config['data']['input_csv']
        
        # Extract raw data
        claims_data = extract_claims_data(csv_path)
        results['steps_completed'].append('extract_raw')
        
        # Filter for CPT procedures
        cpt_data = filter_cpt_procedures(claims_data)
        results['steps_completed'].append('filter_cpt')
        
        # Validate extracted data
        validation_results = validate_extracted_data(cpt_data)
        results['validation_results'] = validation_results
        results['steps_completed'].append('validate_extract')
        
        logger.info(f"✅ Extraction complete: {len(cpt_data):,} CPT procedure records")
        
        if validate_only:
            logger.info("Validation-only mode - stopping after data extraction")
            results['success'] = True
            return results
        
        # Step 2: Transform to OMOP format
        logger.info("🔄 Step 2: Transforming to OMOP format")
        omop_data = transform_all_tables(cpt_data)
        results['steps_completed'].append('transform')
        
        total_omop_records = sum(len(records) for records in omop_data.values())
        results['total_records'] = total_omop_records
        
        logger.info(f"✅ Transformation complete: {total_omop_records:,} OMOP records created")
        
        if skip_load:
            logger.info("Skip-load mode - stopping after transformation")
            results['success'] = True
            return results
        
        # Step 3: Load to database
        logger.info("💾 Step 3: Loading to OMOP database")
        
        # Create connection string
        db_config = config['database']
        connection_string = create_connection_string(
            db_config['host'], str(db_config['port']), db_config['database'],
            db_config['user'], db_config.get('password', '')
        )
        
        # Load data
        load_results = load_omop_tables(omop_data, connection_string, db_config.get('schema', 'public'))
        results['load_results'] = load_results
        results['steps_completed'].append('load')
        
        logger.info(f"✅ Loading complete: {sum(load_results.values()):,} records loaded")
        
        # Step 4: Validate loaded data
        logger.info("🔍 Step 4: Validating loaded data")
        loaded_counts = validate_loaded_data(connection_string, db_config.get('schema', 'public'))
        results['loaded_counts'] = loaded_counts
        results['steps_completed'].append('validate_load')
        
        # Pipeline success
        results['success'] = True
        end_time = time.time()
        results['end_time'] = end_time
        results['duration_seconds'] = end_time - start_time
        
        logger.info(f"🎉 ETL Pipeline completed successfully in {results['duration_seconds']:.2f} seconds")
        
        return results
        
    except Exception as e:
        logger.error(f"❌ ETL Pipeline failed: {e}")
        results['error'] = str(e)
        results['success'] = False
        raise


def main():
    """Main execution function.
    
    Orchestrates the complete Abu Dhabi Claims to OMOP ETL process.
    """
    args = parse_args()
    
    try:
        # Load configuration
        config = load_configuration(args.config, args.environment)
        
        # Validate configuration
        if not validate_configuration(config):
            logger.error("Configuration validation failed")
            sys.exit(1)
        
        if args.dry_run:
            logger.info("✅ Dry run completed - configuration and setup validated")
            return
        
        # Run ETL pipeline
        results = run_etl_pipeline(
            config, 
            input_file=args.input_file,
            skip_load=args.skip_load,
            validate_only=args.validate_only
        )
        
        # Print summary
        if results['success']:
            logger.info("📊 Pipeline Summary:")
            logger.info(f"  Duration: {results.get('duration_seconds', 0):.2f} seconds")
            logger.info(f"  Steps completed: {', '.join(results['steps_completed'])}")
            if 'total_records' in results:
                logger.info(f"  Total OMOP records: {results['total_records']:,}")
            if 'load_results' in results:
                for table, count in results['load_results'].items():
                    logger.info(f"    {table}: {count:,} records")
        
    except Exception as e:
        logger.error(f"Pipeline execution failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
