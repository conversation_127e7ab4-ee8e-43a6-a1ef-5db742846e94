# Abu Dhabi Claims to OMOP ETL - Technical Implementation Guide

## Overview

This guide provides instructions for converting the validated experimental notebook into production-ready Python scripts. The goal is to preserve the working logic from the notebook while creating maintainable, documented code.

## Project Context

### Background
- **Source**: Abu Dhabi healthcare claims data (3,185 records)
- **Target**: OMOP CDM v5.4 (5 core tables)
- **Status**: Logic validated in experimental notebook
- **Goal**: Convert notebook cells into structured Python scripts

### Validated Results (from Notebook)
The experimental notebook successfully transformed:
- **447 unique patients** → PERSON table
- **1,201 visits** → VISIT_OCCURRENCE table  
- **3,185 procedures** → PROCEDURE_OCCURRENCE table
- **3,185 cost records** → COST table
- **171 providers** → PROVIDER table

**Total processed value**: ~AED 520,000
**Processing time**: <2 minutes
**Data integrity**: 100% referential integrity confirmed

## Implementation Task

### Objective
Convert the validated notebook logic into 4-5 simple Python scripts that can be executed independently and maintain the same transformation results.

### Scope
- ✅ **Extract** claims data from CSV
- ✅ **Transform** to 5 OMOP tables (same logic as notebook)
- ✅ **Load** to PostgreSQL database
- ✅ **Document** the transformation logic
- ❌ **NOT** enterprise-grade pipeline (future phase)
- ❌ **NOT** complete OMOP vocabulary integration (future phase)

## Target File Structure

```
src/fhir_omop/etl/abu_dhabi_claims_mvp/
├── extract_claims.py           # CSV data extraction
├── transform_omop.py           # Core transformation logic
├── load_database.py            # Database insertion
├── config.yaml                 # Field mappings and settings
├── run_pipeline.py             # Main execution script
├── README.md                   # Usage instructions
└── learning_notes/
    └── omop_learning_notebook.ipynb  # Reference implementation
```

## Script Specifications

### 1. extract_claims.py
Convert notebook data loading cells into reusable extraction function:

```python
def extract_claims_data(csv_path: str) -> pd.DataFrame:
    """
    Extract claims data from CSV file.
    Based on notebook validation with 3,185 records.
    
    Returns:
        DataFrame with validated claims data
    """
    # Implementation from notebook cells 3-4
```

### 2. transform_omop.py
Convert notebook transformation logic into 5 separate functions:

```python
def transform_to_person(claims_df: pd.DataFrame) -> pd.DataFrame:
    """Generate 447 unique persons from claims data"""
    # Logic from notebook cell 21

def transform_to_visits(claims_df: pd.DataFrame) -> pd.DataFrame:
    """Generate 1,201 visits by aggregating claims"""
    # Logic from notebook cell 22

def transform_to_procedures(claims_df: pd.DataFrame) -> pd.DataFrame:
    """Generate 3,185 procedures (1:1 mapping)"""
    # Logic from notebook cell 25

def transform_to_costs(claims_df: pd.DataFrame) -> pd.DataFrame:
    """Generate 3,185 cost records"""
    # Logic from notebook cell 26

def transform_to_providers(claims_df: pd.DataFrame) -> pd.DataFrame:
    """Generate 171 unique providers"""
    # Logic from notebook cell 28
```

### 3. load_database.py
Convert notebook database insertion cells:

```python
def load_omop_tables(omop_data: dict, connection_string: str):
    """
    Load transformed data to OMOP database.
    Maintains referential integrity from notebook validation.
    """
    # Implementation from notebook cells 23, 24, 27, 29, 30
```

### 4. config.yaml
Externalize hardcoded values from notebook:

```yaml
database:
  host: "localhost"
  port: 5432
  database: "omop_demo"
  schema: "public"

data:
  input_csv: "data/real_test_datasets/claim_anonymized.csv"
  expected_records: 3185
  expected_patients: 447

field_mappings:
  person_id_field: "member_id"
  procedure_code_field: "procedure_code"
  amount_field: "charge_amount"
```

### 5. run_pipeline.py
Main execution script:

```python
def main():
    """
    Execute complete ETL pipeline.
    Reproduces notebook results in automated fashion.
    """
    # Load configuration
    # Extract data
    # Transform to OMOP format
    # Load to database
    # Validate results
```

## Implementation Requirements

### Code Quality
- **Docstrings**: Explain the transformation logic from notebook
- **Type hints**: Basic typing for main functions
- **Error handling**: Basic try/catch for database operations
- **Comments**: Reference notebook cell numbers where logic comes from

### Validation
- **Volume checks**: Ensure output matches notebook results (447/1201/3185/171)
- **Integrity checks**: Verify foreign key relationships
- **Value checks**: Confirm total financial amounts (~AED 520,000)

### Documentation
- **README**: Clear usage instructions
- **Comments**: Explain business logic decisions
- **References**: Link back to notebook cells

## Key Transformation Logic (from Notebook)

### Person Table (447 records)
```python
# From notebook: Group by member_id, take first occurrence
persons = claims_df.groupby('member_id').first().reset_index()
persons['person_id'] = range(1, len(persons) + 1)
```

### Visit Occurrence (1,201 records)  
```python
# From notebook: Group by patient + date
visits = claims_df.groupby(['member_id', 'service_from_date']).agg({
    'place_of_service': 'first',
    'provider_name': 'first'
}).reset_index()
```

### Procedure Occurrence (3,185 records)
```python
# From notebook: 1:1 mapping preserving all procedures
procedures = claims_df.copy()
procedures['procedure_occurrence_id'] = range(1, len(procedures) + 1)
```

### Cost Table (3,185 records)
```python
# From notebook: Link to procedures via cost_event_id
costs = claims_df[['charge_amount']].copy()
costs['cost_event_id'] = procedures['procedure_occurrence_id']
```

### Provider Table (171 records)
```python
# From notebook: Deduplicate by provider name
providers = claims_df.groupby('provider_name').first().reset_index()
providers['provider_id'] = range(1, len(providers) + 1)
```

## Success Criteria

### Functional Requirements
- ✅ Process the same CSV file used in notebook
- ✅ Generate exactly 5 OMOP tables with validated record counts
- ✅ Maintain 100% referential integrity 
- ✅ Reproduce total financial amounts

### Technical Requirements
- ✅ Clean, readable Python code
- ✅ Basic error handling and logging
- ✅ Configuration externalized to YAML
- ✅ Execution time < 5 minutes

### Documentation Requirements
- ✅ Clear README with usage examples
- ✅ Docstrings explaining transformation logic
- ✅ Comments referencing notebook cells
- ✅ Link to original notebook as reference

## Development Steps

### Phase 1: Core Scripts (Week 1)
1. **Create extract_claims.py** - Convert notebook data loading
2. **Create transform_omop.py** - Convert transformation cells
3. **Create load_database.py** - Convert database insertion
4. **Create config.yaml** - Externalize configuration

### Phase 2: Integration (Week 2)
1. **Create run_pipeline.py** - Main execution script
2. **Add basic validation** - Volume and integrity checks
3. **Update README** - Usage instructions and examples
4. **Test with original data** - Confirm results match notebook

## Reference Documentation

### Primary Sources
- **Experimental notebook**: `learning_notes/omop_learning_notebook.ipynb`
- **OMOP CDM v5.4**: https://github.com/OHDSI/CommonDataModel/tree/v5.4.0
- **Project architecture**: `../../docs/architecture/reference_architecture.md`

### Secondary Sources
- **PostgreSQL setup**: `../../docs/guides/omop/database/postgresql_setup.md`
- **OMOP overview**: `../../docs/guides/omop/introduction.md`

## Expected Deliverables

1. **5 Python scripts** implementing notebook logic
2. **1 YAML config** with externalized settings
3. **Updated README** with clear usage instructions
4. **Validation results** confirming notebook reproduction

## Notes

- **Keep it simple**: Focus on converting notebook logic, not building enterprise pipeline
- **Preserve knowledge**: Document why transformations work with this specific dataset
- **Reference notebook**: Link back to original cells for context
- **Future extensibility**: Code should be readable for future enhancement phases

---

**Created**: June 4, 2025  
**Target completion**: 2 weeks  
**Next phase**: Complete OMOP database setup (separate initiative)
