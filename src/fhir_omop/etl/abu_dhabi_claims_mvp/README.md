# Abu Dhabi Claims to OMOP ETL - MVP

## Status
- ✅ **Experimental validation completed** (447 patients, 1,201 visits, 3,185 procedures)
- ✅ **Production scripts implemented** (notebook logic converted to reusable scripts)

## Quick Start

### 1. Install Dependencies
```bash
# Activate the fhir-omop conda environment
conda activate fhir-omop

# Ensure all dependencies are installed
pip install pandas sqlalchemy psycopg2-binary pyyaml python-dotenv
```

### 2. Configure Database
```bash
# Update config.yaml with your database settings
# Default configuration uses local PostgreSQL:
# - Host: localhost
# - Port: 5432
# - Database: omop_abu_dhabi
# - User: jaimepm
# - Schema: public
```

### 3. Run ETL Pipeline
```bash
# Run complete ETL pipeline
python run_pipeline.py

# Or run with custom configuration
python run_pipeline.py --config custom_config.yaml

# Validate data only (no transformation/loading)
python run_pipeline.py --validate-only

# Transform only (skip database loading)
python run_pipeline.py --skip-load
```

**Expected output:**
- 447 unique patients → PERSON table
- 1,201 visits (aggregated by patient+date) → VISIT_OCCURRENCE table
- 3,185 procedures (1:1 from claims) → PROCEDURE_OCCURRENCE table
- 171 unique providers → PROVIDER table
- ~AED 520,000 total processed value

## Implementation Structure

**Implemented scripts:**
```
src/fhir_omop/etl/abu_dhabi_claims_mvp/
├── extract_claims.py           # ✅ CSV data extraction
├── transform_omop.py           # ✅ 5 transformation functions
├── load_database.py            # ✅ PostgreSQL insertion
├── config.yaml                 # ✅ Configuration settings
├── run_pipeline.py             # ✅ Main execution script
├── README.md                   # ✅ Usage instructions
└── learning_notes/
    └── omop_learning_notebook.ipynb  # 📚 Reference implementation
```

## Usage Examples

### Individual Script Usage

#### Extract Claims Data
```bash
# Extract and validate claims data
python extract_claims.py --input-file data/real_test_datasets/claim_anonymized.csv

# Save extracted data to file
python extract_claims.py --output-file temp/extracted_claims.csv

# Validate data only
python extract_claims.py --validate-only
```

#### Transform to OMOP Format
```python
# Use in Python scripts
from extract_claims import extract_claims_data, filter_cpt_procedures
from transform_omop import transform_all_tables

# Extract data
claims_data = extract_claims_data("data/real_test_datasets/claim_anonymized.csv")
cpt_data = filter_cpt_procedures(claims_data)

# Transform to OMOP
omop_data = transform_all_tables(cpt_data)
print(f"Created {sum(len(records) for records in omop_data.values())} OMOP records")
```

#### Load to Database
```bash
# Test database connection
python load_database.py --dry-run

# Load with custom database settings
python load_database.py --db-host localhost --db-port 5432 --db-name omop_test
```

### Configuration Options

#### Environment Variables
```bash
# Set environment variables for database connection
export OMOP_DB_HOST=localhost
export OMOP_DB_PORT=5432
export OMOP_DB_NAME=omop_abu_dhabi
export OMOP_DB_USER=jaimepm
export INPUT_FILE=data/real_test_datasets/claim_anonymized.csv

# Run pipeline with environment variables
python run_pipeline.py
```

#### Custom Configuration
```yaml
# custom_config.yaml
database:
  host: "your-db-host"
  port: 5432
  database: "your_omop_db"
  user: "your_user"
  password: "your_password"

data:
  input_csv: "path/to/your/claims.csv"
```

```bash
# Use custom configuration
python run_pipeline.py --config custom_config.yaml
```

## Key Design Decisions (from Notebook)

### Transformation Logic Validated
1. **Person**: Deduplicate by `aio_patient_id` → 447 unique patients
2. **Visits**: Group by `case` (encounter) → 1,201 visits
3. **Procedures**: 1:1 mapping from claims → 3,185 procedures
4. **Costs**: Link to procedures via `activity_id` → 3,185 cost records
5. **Providers**: Deduplicate by `clinician` → 171 providers

### Technical Stack
- **Python 3.11** with pandas, SQLAlchemy, PyYAML
- **PostgreSQL** with basic OMOP CDM schema
- **YAML configuration** for field mappings and settings
- **Comprehensive validation** and error handling
- **Logging** for monitoring and debugging

## Success Criteria

The Python scripts achieve:
- ✅ Process the same Abu Dhabi claims CSV (3,185 records)
- ✅ Generate exactly 5 OMOP tables with validated volumes
- ✅ Maintain 100% referential integrity
- ✅ Complete processing in <5 minutes
- ✅ Include comprehensive error handling and logging
- ✅ Follow project coding standards (NumPy docstrings, type hints)
- ✅ Externalized configuration via YAML
## Troubleshooting

### Common Issues

#### Database Connection Errors
```bash
# Test database connection
python load_database.py --dry-run

# Check PostgreSQL is running
pg_isready -h localhost -p 5432

# Verify database exists
psql -h localhost -U jaimepm -l
```

#### Data Validation Failures
```bash
# Check input file exists and has correct format
python extract_claims.py --validate-only

# Verify expected columns are present
head -1 data/real_test_datasets/claim_anonymized.csv
```

#### Memory Issues with Large Datasets
```yaml
# Adjust batch size in config.yaml
processing:
  batch_size: 500
  chunk_size: 5000
```

### Validation Metrics

The pipeline validates against these expected metrics from the notebook:
- **Total records**: 3,185 CPT procedures
- **Unique patients**: 447 individuals
- **Unique visits**: 1,201 encounters
- **Unique providers**: 171 clinicians
- **Financial total**: ~AED 520,000

If your data doesn't match these metrics, check:
1. Input file path is correct
2. Data format matches expected CSV structure
3. CPT code filtering is working (5-digit numeric codes)

## Reference Documentation

### Primary Sources
- **Experimental notebook**: `learning_notes/omop_learning_notebook.ipynb` (source of truth)
- **Technical implementation guide**: `TECHNICAL_IMPLEMENTATION_GUIDE.md`
- **OMOP CDM v5.4**: https://github.com/OHDSI/CommonDataModel/tree/v5.4.0

### Project Standards
- **Development standards**: `../../docs/guides/development/standards.md`
- **Project architecture**: `../../docs/architecture/reference_architecture.md`
- **OMOP guidance**: `../../docs/guides/omop/`

---

## Implementation Notes

### Conversion from Notebook
These scripts are direct conversions of the validated logic from `learning_notes/omop_learning_notebook.ipynb`:

- **extract_claims.py**: Cells 3-4 (data loading and CPT filtering)
- **transform_omop.py**: Cells 21, 22, 25, 26, 28 (transformation functions)
- **load_database.py**: Cells 23, 24, 27, 29, 30 (database insertion)
- **config.yaml**: Externalized hardcoded values from notebook
- **run_pipeline.py**: Complete notebook execution flow

### Future Enhancements
This MVP implementation provides a foundation for:
- Scaling to larger datasets
- Adding more OMOP domains
- Implementing vocabulary mappings
- Integration with FHIR server
- Production deployment features

**Status**: ✅ Production-ready scripts implementing validated notebook logic
**Created**: December 2024
**Team**: FHIR-OMOP Development Team



